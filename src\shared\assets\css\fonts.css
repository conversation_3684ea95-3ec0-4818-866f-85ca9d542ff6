/**
 * Font Configuration for MtfGame
 * Improved font loading and fallbacks
 */

/* Import Google Fonts with display=swap for better performance */
@import url('google-fonts-inter-jetbrains.css');

/* Font Face Declarations with Local Fallbacks */
@font-face {
    font-family: 'Inter-Fallback';
    src: local('Inter'), local('SF Pro Display'), local('Segoe UI'), local('Roboto'), local('Helvetica Neue'), local('Arial');
    font-display: swap;
}

@font-face {
    font-family: 'JetBrains-Mono-Fallback';
    src: local('JetBrains Mono'), local('Consolas'), local('Monaco'), local('Courier New'), local('monospace');
    font-display: swap;
}

/* Root Font Variables */
:root {
    --font-primary: 'Inter', 'Inter-Fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-gaming: 'JetBrains Mono', 'JetBrains-Mono-Fallback', Consolas, Monaco, 'Courier New', monospace;
    --font-display: 'Inter', 'Inter-Fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'JetBrains-Mono-Fallback', Consolas, Monaco, 'Courier New', monospace;
}

/* Base Font Settings */
* {
    font-family: var(--font-primary);
}

html, body {
    font-family: var(--font-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1, "liga" 1;
}

/* Font Class Utilities */
.font-primary {
    font-family: var(--font-primary) !important;
}

.font-gaming {
    font-family: var(--font-gaming) !important;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.font-display {
    font-family: var(--font-display) !important;
    font-weight: 600;
}

.font-mono {
    font-family: var(--font-mono) !important;
}

/* Form Elements Font Inheritance */
input, textarea, select, button {
    font-family: inherit;
}

/* Specific Element Font Fixes */
.btn, button {
    font-family: var(--font-primary);
    font-weight: 500;
}

/* Gaming Theme Typography */
.gaming-title {
    font-family: var(--font-gaming);
    font-weight: 700;
    letter-spacing: 0.05em;
}

.gaming-text {
    font-family: var(--font-gaming);
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Error Page Font Fix */
.error-code {
    font-family: var(--font-gaming);
}

/* Header and Navigation Font */
.nav-link, .nav-item {
    font-family: var(--font-primary);
    font-weight: 500;
}

/* Logo and Brand Font */
.brand, .logo-text {
    font-family: var(--font-gaming);
    font-weight: 700;
}

/* Form Labels and Inputs */
.form-label {
    font-family: var(--font-primary);
    font-weight: 500;
}

.form-input, .form-control {
    font-family: var(--font-primary);
}

/* Table Font */
.table, .table th, .table td {
    font-family: var(--font-primary);
}

/* Card and Panel Font */
.card-title, .panel-title {
    font-family: var(--font-display);
    font-weight: 600;
}

/* Alert and Notification Font */
.alert, .notification, .toast {
    font-family: var(--font-primary);
}

/* Responsive Font Sizes */
@media (max-width: 768px) {
    .gaming-title {
        letter-spacing: 0.025em;
    }
    
    .error-code {
        font-size: 5rem;
    }
}

/* Font Loading Optimization */
.font-loading {
    font-display: swap;
    visibility: hidden;
}

.font-loaded .font-loading {
    visibility: visible;
}

/* Prevent FOIT (Flash of Invisible Text) */
.font-fallback {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Vietnamese Character Support */
@supports (font-variant-ligatures: common-ligatures) {
    body {
        font-variant-ligatures: common-ligatures;
    }
}

/* Print Styles */
@media print {
    * {
        font-family: 'Times New Roman', serif !important;
    }
}
