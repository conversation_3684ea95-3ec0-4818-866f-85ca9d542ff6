<?php

namespace Pay\Controllers;

use Core\Controller;

/**
 * Game Guide Controller for Pay Domain
 * Handles game deposit guide pages
 */
class GameGuideController extends Controller
{
    public function __construct()
    {
        parent::__construct();

        // Override view to use Pay domain views
        $payViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($payViewPath);
    }

    /**
     * Show game deposit guide
     */
    public function guide(string $gameId): void
    {
        // Check authentication first - redirect to id domain if not logged in
        if (!$this->isAuthenticated()) {
            $this->redirectToIdDomain("/game/{$gameId}/guide");
            return;
        }

        // Get game information
        $game = $this->getGameById($gameId);

        if (!$game) {
            // Game not found, redirect to home
            header("Location: " . $this->url('/'));
            exit;
        }

        // Get deposit packages for this game
        $packages = $this->getGameDepositPackages($gameId);

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('game_guide', [
            'game_name' => $game['name']
        ]);
        $structuredData = $this->seoHelper->generateStructuredData('game_guide', [
            'game_name' => $game['name']
        ]);

        $this->render('game/guide', array_merge($this->getThemeData(), [
            'title' => 'Hướng dẫn nạp ' . $game['name'],
            'game' => $game,
            'packages' => $packages,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Get game by ID
     */
    private function getGameById(string $gameId): ?array
    {
        $games = [
            'lua_game' => [
                'id' => 'lua_game',
                'name' => 'Nạp Lúa',
                'description' => 'Nạp lúa để quy đổi vào game bất kỳ',
                'image' => 'coin_big.png',
                'status' => 'online',
                'currency' => 'Lúa',
                'currency_icon' => 'fas fa-coins'
            ],
            'adventure_game' => [
                'id' => 'adventure_game',
                'name' => 'Adventure Quest',
                'description' => 'Game phiêu lưu và khám phá thế giới mới với đồ họa tuyệt đẹp',
                'image' => 'game01.png',
                'status' => 'online',
                'currency' => 'Lúa',
                'currency_icon' => 'fas fa-coins'
            ]
        ];

        return $games[$gameId] ?? null;
    }

    /**
     * Get deposit packages for game
     */
    private function getGameDepositPackages(string $gameId): array
    {
        // Base packages
        $basePackages = [
            [
                'amount' => 20000,
                'currency_amount' => 20,
                'bonus' => 0,
                'total_currency' => 20,
                'popular' => false
            ],
            [
                'amount' => 50000,
                'currency_amount' => 50,
                'bonus' => 5,
                'total_currency' => 55,
                'popular' => true
            ],
            [
                'amount' => 100000,
                'currency_amount' => 100,
                'bonus' => 15,
                'total_currency' => 115,
                'popular' => false
            ],
            [
                'amount' => 200000,
                'currency_amount' => 200,
                'bonus' => 35,
                'total_currency' => 235,
                'popular' => false
            ],
            [
                'amount' => 500000,
                'currency_amount' => 500,
                'bonus' => 100,
                'total_currency' => 600,
                'popular' => false
            ],
            [
                'amount' => 1000000,
                'currency_amount' => 1000,
                'bonus' => 250,
                'total_currency' => 1250,
                'popular' => false
            ]
        ];

        return $basePackages;
    }

    /**
     * Redirect to ID domain for authentication
     */
    private function redirectToIdDomain(string $returnPath): void
    {
        // Build return URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $returnUrl = urlencode($protocol . '://' . $host . $returnPath);

        // Use Controller's method to get login URL
        $loginUrl = $this->getLoginUrl($returnUrl);

        header("Location: " . $loginUrl);
        exit;
    }
}
