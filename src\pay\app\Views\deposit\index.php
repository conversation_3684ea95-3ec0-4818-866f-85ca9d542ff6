<?php $this->startSection('content'); ?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-12">
    <!-- Page Header -->
    <div class="text-center mb-6 sm:mb-8">
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2"><?= $__('deposit.view.title') ?></h1>
        <p class="text-gray-600 text-sm sm:text-base"><?= $__('deposit.view.select_package') ?></p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <div class="bg-white border-2 border-gray-300 rounded-xl shadow-xl p-4 sm:p-6">
                <form id="depositForm">
                        <!-- Amount Selection -->
                        <div class="mb-6">
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">1. <?= $__('deposit.view.select_amount') ?></h3>

                            <!-- Quick Packages -->
                            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 sm:gap-3 mb-4">
                                <?php foreach ($packages as $package): ?>
                                <div class="package-item relative bg-gray-50 border-2 border-gray-200 rounded-lg p-2 sm:p-3 cursor-pointer hover:border-blue-500 transition-all <?= ($package['popular'] ?? false) ? 'border-blue-500 bg-blue-50' : '' ?>" data-amount="<?= $package['amount'] ?>" data-total="<?= $package['total'] ?? $package['amount'] ?>">
                                    <?php if ($package['popular'] ?? false): ?>
                                    <div class="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full">HOT</div>
                                    <?php endif; ?>

                                    <div class="text-center">
                                        <div class="text-sm sm:text-base lg:text-lg font-bold text-gray-900 leading-tight"><?= number_format($package['amount'], 0, ',', '.') ?></div>
                                        <div class="text-xs text-gray-600">VNĐ</div>
                                        <?php if ($package['bonus_percent'] > 0): ?>
                                        <div class="text-xs text-emerald-600 font-semibold">+<?= $package['bonus_percent'] ?>%</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Selected Package Info -->
                            <div id="selectedPackageInfo" class="bg-blue-50 border-2 border-blue-200 rounded-lg p-4 hidden">
                                <div class="text-center">
                                    <div class="text-sm text-gray-600 mb-1">Bạn sẽ nhận được:</div>
                                    <div class="text-xl font-bold text-blue-600">
                                        <img src="<?= $this->asset('images/coin_small.png') ?>" alt="lúa" class="inline w-5 h-5 mr-1">
                                        <span id="totalRiceAmount">0</span> lúa
                                    </div>
                                    <input type="hidden" name="amount" id="selectedAmount">
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method Selection -->
                        <div class="mb-6">
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4">2. <?= $__('deposit.view.payment_method') ?></h3>

                            <?php foreach ($payment_methods as $categoryKey => $category): ?>
                            <div class="mb-6">
                                <h4 class="text-base font-semibold text-gray-900 mb-4 flex items-center">
                                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-<?= $categoryKey === 'banks' ? 'university' : 'wallet' ?> text-white text-sm"></i>
                                    </div>
                                    <?= $this->escape($category['title']) ?>
                                </h4>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                    <?php foreach ($category['methods'] as $key => $method): ?>
                                    <div class="payment-method bg-white border-2 border-gray-200 rounded-xl p-4 cursor-pointer hover:border-blue-500 hover:shadow-lg transition-all duration-200" data-method="<?= $key ?>" data-min-amount="<?= $method['min_amount'] ?>">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center flex-shrink-0">
                                                <i class="<?= $method['icon'] ?> text-xl text-gray-700"></i>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div class="font-semibold text-gray-900 text-sm sm:text-base"><?= $this->escape($method['name']) ?></div>
                                                <div class="text-xs sm:text-sm text-gray-600 truncate"><?= $this->escape($method['description']) ?></div>
                                                <?php if ($method['min_amount'] >= 20000): ?>
                                                <div class="text-xs text-orange-600 font-medium mt-1">Tối thiểu: <?= number_format($method['min_amount']) ?> VNĐ</div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                                <input type="radio" name="payment_method" value="<?= $key ?>" class="sr-only">
                                                <div class="w-2.5 h-2.5 bg-blue-600 rounded-full opacity-0 transition-opacity"></div>
                                            </div>
                                        </div>

                                        <!-- Bank Selection for methods that require it -->
                                        <?php if (!empty($method['banks']) && is_array($method['banks'])): ?>
                                        <div class="bank-selection mt-4 hidden">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Chọn ngân hàng:</label>
                                            <select name="bank_code_<?= $key ?>" class="w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white">
                                                <option value="">-- Chọn ngân hàng --</option>
                                                <?php foreach ($method['banks'] as $bankCode => $bankName): ?>
                                                <option value="<?= $bankCode ?>"><?= $this->escape($bankName) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Submit Button -->
                        <div class="text-center">
                            <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-semibold text-base sm:text-lg hover:from-emerald-600 hover:to-blue-700 transition-all">
                                <i class="fas fa-credit-card mr-2"></i>
                                <span class="btn-text"><?= $__('shared.banners.deposit_now') ?></span>
                            </button>
                        </div>
                    </form>
            </div>
        </div>

        <!-- Sidebar Info -->
        <div class="space-y-6">

            <!-- Game Info -->
            <div class="bg-white border-2 border-gray-300 rounded-xl shadow-xl p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-coins text-blue-600 mr-2"></i>
                    <?= $__('deposit.view.info') ?>
                </h4>
                <ul class="space-y-3 text-sm text-gray-700">
                    <li class="flex items-start">
                        <i class="fas fa-clock text-green-500 mr-3 mt-0.5"></i>
                        <span><?= $__('deposit.view.auto_processing') ?> <strong>24/7</strong></span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-shield-alt text-green-500 mr-3 mt-0.5"></i>
                        <span><?= $__('deposit.view.absolute_security') ?></span>
                    </li>
                </ul>
            </div>

            <!-- Support -->
            <div class="bg-white border-2 border-gray-300 rounded-xl shadow-xl p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-3">
                    <i class="fas fa-headset text-blue-600 mr-2"></i>
                    <?= $__('deposit.view.need_support') ?>?
                </h4>
                <p class="text-gray-600 mb-4 text-sm"><?= $__('deposit.view.support_message') ?></p>

                <div class="space-y-3">
                    <a href="#" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all inline-flex items-center justify-center">
                        <i class="fas fa-comments mr-2"></i>
                        <?= $__('deposit.view.contact_24_7') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Package selection
    $('.package-item').click(function() {
        $('.package-item').removeClass('border-blue-500 bg-blue-50').addClass('border-gray-200 bg-gray-50');
        $(this).removeClass('border-gray-200 bg-gray-50').addClass('border-blue-500 bg-blue-50');

        const amount = $(this).data('amount');
        const total = $(this).data('total');

        // Update hidden input
        $('#selectedAmount').val(amount);

        // Calculate and show rice amount
        const riceAmount = Math.floor((total / 10000) * 1000);
        $('#totalRiceAmount').text(riceAmount.toLocaleString());
        $('#selectedPackageInfo').removeClass('hidden');
    });

    // Payment method selection
    $('.payment-method').click(function() {
        $('.payment-method').removeClass('border-blue-500 bg-blue-50').addClass('border-gray-200 bg-gray-50');
        $('.payment-method .w-2').removeClass('opacity-100').addClass('opacity-0');
        $('.bank-selection').addClass('hidden');

        $(this).removeClass('border-gray-200 bg-gray-50').addClass('border-blue-500 bg-blue-50');
        $(this).find('.w-2').removeClass('opacity-0').addClass('opacity-100');

        const method = $(this).data('method');
        $(`input[name="payment_method"][value="${method}"]`).prop('checked', true);

        // Show bank selection if available
        const bankSelection = $(this).find('.bank-selection');
        if (bankSelection.length > 0) {
            bankSelection.removeClass('hidden');
        }

        // Validate amount against minimum
        const minAmount = parseInt($(this).data('min-amount'));
        const currentAmount = parseInt($('#amount').val());

        if (currentAmount && currentAmount < minAmount) {
            GamingApp.showNotification('warning', 'Số tiền tối thiểu cho phương thức này là ' + minAmount.toLocaleString() + ' VNĐ');
            $('#amount').val(minAmount);
        }
    });

    // Form submission
    $('#depositForm').submit(function(e) {
        e.preventDefault();

        const amount = parseInt($('#selectedAmount').val());
        const paymentMethod = $('input[name="payment_method"]:checked').val();
        const selectedMethod = $('.payment-method.border-blue-500');
        const bankCode = selectedMethod.find(`select[name="bank_code_${paymentMethod}"]`).val();

        if (!amount) {
            GamingApp.showNotification('Vui lòng chọn gói nạp tiền', 'error');
            return;
        }

        if (!paymentMethod) {
            GamingApp.showNotification('Vui lòng chọn phương thức thanh toán', 'error');
            return;
        }

        // Check if bank selection is required but not selected
        if (selectedMethod.find('.bank-selection').length > 0 && !bankCode) {
            GamingApp.showNotification('Vui lòng chọn ngân hàng', 'error');
            return;
        }

        const submitBtn = $('button[type="submit"]');
        const originalText = submitBtn.find('.btn-text').text();

        submitBtn.prop('disabled', true).find('.btn-text').text('Đang xử lý...');

        $.ajax({
            url: '<?= $this->url('/deposit') ?>',
            method: 'POST',
            data: {
                amount: amount,
                payment_method: paymentMethod,
                bank_code: bankCode || ''
            },
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');

                    // Show payment instructions
                    showPaymentInstructions(response.data);
                } else {
                    GamingApp.showNotification(response.message, 'error');
                }
                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                GamingApp.showNotification(response?.message || 'Đã xảy ra lỗi. Vui lòng thử lại.', 'error');
                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            }
        });
    });

    function showPaymentInstructions(data) {
        // Redirect to Ngân Lượng checkout if available
        if (data && data.checkout_url) {
            window.location.href = data.checkout_url;
            return;
        }

        // Fallback: show order information
        console.log('Order created:', data);
        GamingApp.showNotification('Đơn hàng đã được tạo thành công', 'success');
    }
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
