<?php

namespace Core;

/**
 * View Class
 * Handles template rendering and view logic
 */
class View
{
    private array $viewPaths = [];
    private array $data = [];
    private array $sections = [];
    private string $currentSection = '';

    public function __construct(string $viewPath = null)
    {
        // Set up multiple view paths for shared views
        $this->viewPaths = [
            $viewPath ?: __DIR__ . '/../app/Views/',  // Domain-specific views
            __DIR__ . '/../shared/Views/'              // Shared views
        ];
    }

    /**
     * Render a view template
     */
    public function render(string $template, array $data = []): void
    {
        // Add global services to data
        $globalData = $this->getGlobalData();
        $this->data = array_merge($this->data, $globalData, $data);

        $templateFile = $this->findViewFile($template);

        if (!$templateFile) {
            throw new \Exception("View template {$template} not found in any view paths");
        }

        // Extract data to variables
        extract($this->data);

        // Start output buffering
        ob_start();

        // Include the template
        include $templateFile;

        // Get the content
        $content = ob_get_clean();

        // Output the content
        echo $content;
    }

    /**
     * Find view file in multiple paths
     */
    private function findViewFile(string $template): ?string
    {
        $templatePath = str_replace('.', '/', $template) . '.php';

        foreach ($this->viewPaths as $viewPath) {
            $fullPath = $viewPath . $templatePath;
            if (file_exists($fullPath)) {
                return $fullPath;
            }
        }

        return null;
    }

    /**
     * Get global data available to all views
     */
    private function getGlobalData(): array
    {
        $globalData = [];

        try {
            // Make FirebaseService available to all views
            $firebaseService = new \Shared\Services\FirebaseService();
            $globalData['firebaseService'] = $firebaseService;
            $globalData['firebase_config'] = $firebaseService->getFirebaseConfig();
        } catch (\Exception $e) {
            // If Firebase service fails, continue without it
            $globalData['firebaseService'] = null;
            $globalData['firebase_config'] = null;
        }

        return $globalData;
    }

    /**
     * Format balance for display (deprecated - use currency() instead)
     */
    public function formatBalance($balance): string
    {
        return number_format((float)$balance, 0, ',', '.');
    }

    /**
     * Format balance with coin icon
     */
    public function balanceWithCoin($balance, string $size = 'small'): string
    {
        return $this->currency((float)$balance, $size);
    }

    /**
     * Set global view data
     */
    public function share(string $key, $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * Start a section
     */
    public function startSection(string $name): void
    {
        $this->currentSection = $name;
        ob_start();
    }

    /**
     * End current section
     */
    public function endSection(): void
    {
        if (empty($this->currentSection)) {
            throw new \Exception("No section started");
        }

        $this->sections[$this->currentSection] = ob_get_clean();
        $this->currentSection = '';
    }

    /**
     * Yield section content
     */
    public function yieldSection(string $name, string $default = ''): string
    {
        return $this->sections[$name] ?? $default;
    }

    /**
     * Include a partial view
     */
    public function include(string $partial, array $data = []): void
    {
        // Handle both dot notation and direct path
        if (str_contains($partial, '.')) {
            $partialFile = $this->findViewFile($partial);
        } else {
            $partialFile = $this->findViewFile('partials/' . $partial);
        }

        if (!$partialFile) {
            throw new \Exception("Partial view {$partial} not found in any view paths");
        }

        // Merge data
        $partialData = array_merge($this->data, $data);
        extract($partialData);

        include $partialFile;
    }

    /**
     * Escape HTML output
     */
    public function escape(string $value): string
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public function url(string $path = ''): string
    {
        $app = Application::getInstance();
        $baseUrl = rtrim($app->getBaseUrl(), '/');
        return $baseUrl . '/' . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public function asset(string $path): string
    {
        $app = Application::getInstance();
        $baseUrl = rtrim($app->getBaseUrl(), '/');
        return $baseUrl . '/assets/' . ltrim($path, '/');
    }

    /**
     * Check if user is authenticated
     */
    public function isAuth(): bool
    {
        return isset($_SESSION['user']);
    }

    /**
     * Get current user
     */
    public function user(): ?array
    {
        return $_SESSION['user'] ?? null;
    }

    /**
     * Get and display flash messages using GamingApp.showNotification()
     */
    public function flash(): string
    {
        if (!isset($_SESSION['flash'])) {
            return '';
        }

        $script = '<script>';
        $script .= 'document.addEventListener("DOMContentLoaded", function() {';

        foreach ($_SESSION['flash'] as $type => $message) {
            $escapedMessage = $this->escape($message);
            $script .= "GamingApp.showNotification('{$escapedMessage}', '{$type}');";
        }

        $script .= '});';
        $script .= '</script>';

        // Clear flash messages
        unset($_SESSION['flash']);

        return $script;
    }

    /**
     * Format currency with coin icon
     */
    public function currency(float $amount, string $size = 'small'): string
    {
        $coinIcon = $size === 'big' ? 'coin_big.png' : 'coin_small.png';

        // Different sizes for different contexts
        $iconSize = match($size) {
            'big' => 'w-6 h-6',
            'medium' => 'w-5 h-5',
            'small' => 'w-4 h-4',
            'tiny' => 'w-3 h-3',
            default => 'w-4 h-4'
        };

        return number_format($amount, 0, ',', '.') . ' <img src="' . $this->asset('images/' . $coinIcon) . '" alt="Coin" class="inline-block ' . $iconSize . ' ml-1" style="vertical-align: middle;">';
    }

    /**
     * Format date
     */
    public function date(string $date, string $format = 'd/m/Y H:i'): string
    {
        return date($format, strtotime($date));
    }

    /**
     * Get CSRF token
     */
    public function csrfToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Generate CSRF input field
     */
    public function csrfField(): string
    {
        return '<input type="hidden" name="csrf_token" value="' . $this->csrfToken() . '">';
    }

    /**
     * Truncate text
     */
    public function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . $suffix;
    }
}
