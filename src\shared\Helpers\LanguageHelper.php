<?php

namespace Shared\Helpers;

/**
 * Language Helper Class
 * Manages translations and language switching
 */
class LanguageHelper
{
    private static ?LanguageHelper $instance = null;
    private array $translations = [];
    private string $currentLanguage;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct(string $language = 'vi')
    {
        $this->currentLanguage = $language;
        $this->loadTranslations();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(string $language = 'vi'): LanguageHelper
    {
        if (self::$instance === null) {
            self::$instance = new self($language);
        }
        return self::$instance;
    }

    /**
     * Load translations for current language
     */
    private function loadTranslations(): void
    {
        $translationsPath = __DIR__ . '/translations/';

        // Load Vietnamese translations
        $viFile = $translationsPath . 'vi.php';
        if (file_exists($viFile)) {
            $this->translations['vi'] = include $viFile;
        }

        // Load English translations
        $enFile = $translationsPath . 'en.php';
        if (file_exists($enFile)) {
            $this->translations['en'] = include $enFile;
        }

        // Fallback if files don't exist
        if (empty($this->translations)) {
            $this->translations = [
                'vi' => ['home' => 'Trang chủ', 'login' => 'Đăng nhập'],
                'en' => ['home' => 'Home', 'login' => 'Login']
            ];
        }
    }

    /**
     * Get translation for a key (supports dot notation for nested arrays)
     */
    public function translate(string $key, array $params = []): string
    {
        $translation = $this->getNestedValue($this->translations[$this->currentLanguage], $key) ??
                      $this->getNestedValue($this->translations['vi'], $key) ??
                      $key;

        // Replace parameters in translation
        foreach ($params as $param => $value) {
            $translation = str_replace("{{$param}}", $value, $translation);
        }

        return $translation;
    }

    /**
     * Get nested array value using dot notation
     */
    private function getNestedValue(array $array, string $key)
    {
        $keys = explode('.', $key);
        $value = $array;

        foreach ($keys as $nestedKey) {
            if (!is_array($value) || !array_key_exists($nestedKey, $value)) {
                return null;
            }
            $value = $value[$nestedKey];
        }

        return $value;
    }

    /**
     * Set current language
     */
    public function setLanguage(string $language): void
    {
        if (isset($this->translations[$language])) {
            $this->currentLanguage = $language;
        }
    }

    /**
     * Get current language
     */
    public function getCurrentLanguage(): string
    {
        return $this->currentLanguage;
    }

    /**
     * Get all available languages
     */
    public function getAvailableLanguages(): array
    {
        return array_keys($this->translations);
    }
}

/**
 * Global translation function
 */
function __($key, $params = []) {
    $language = $_SESSION['language'] ?? 'vi';
    $helper = LanguageHelper::getInstance($language);
    return $helper->translate($key, $params);
}
