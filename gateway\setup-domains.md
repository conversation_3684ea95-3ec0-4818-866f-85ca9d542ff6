# 🌐 Cấu hình Domains cho MTF Game Gateway

## 📋 Thông tin cấu hình

### 🎯 Domains cần setup:
- `pay.mtfgame.com` → Project hiện tại
- `id.mtfgame.com` → Project hiện tại

### 🔧 Target containers:
- **Current Project**: `localhost:8080` (sau khi đổi port)

## 🚀 Hướng dẫn setup từng bước:

### 1. T<PERSON>y cập Admin Interface
```
URL: http://localhost:81
Email: <EMAIL>
Password: changeme
```

### 2. Đổi password mặc định
- Vào **Users** → **Edit admin user**
- Đổi email và password mới

### 3. Thêm Proxy Host cho pay.mtfgame.com

**Proxy Hosts** → **Add Proxy Host**

**Details Tab:**
- Domain Names: `pay.mtfgame.com`
- Scheme: `http://`
- Forward Hostname/IP: `host.docker.internal` (Windows/Mac) hoặc `**********` (Linux)
- Forward Port: `8080`
- Cache Assets: ✅ Enabled
- Block Common Exploits: ✅ Enabled
- Websockets Support: ✅ Enabled

**SSL Tab:**
- SSL Certificate: `Request a new SSL Certificate`
- Force SSL: ✅ Enabled
- HTTP/2 Support: ✅ Enabled
- HSTS Enabled: ✅ Enabled
- Email: `<EMAIL>`
- I Agree to the Let's Encrypt Terms of Service: ✅

### 4. Thêm Proxy Host cho id.mtfgame.com

**Proxy Hosts** → **Add Proxy Host**

**Details Tab:**
- Domain Names: `id.mtfgame.com`
- Scheme: `http://`
- Forward Hostname/IP: `host.docker.internal` (Windows/Mac) hoặc `**********` (Linux)
- Forward Port: `8080`
- Cache Assets: ✅ Enabled
- Block Common Exploits: ✅ Enabled
- Websockets Support: ✅ Enabled

**SSL Tab:**
- SSL Certificate: `Request a new SSL Certificate`
- Force SSL: ✅ Enabled
- HTTP/2 Support: ✅ Enabled
- HSTS Enabled: ✅ Enabled
- Email: `<EMAIL>`
- I Agree to the Let's Encrypt Terms of Service: ✅

## 🔧 Advanced Configuration (Optional)

### Custom Nginx Configuration
Nếu cần custom config, thêm vào **Advanced Tab**:

```nginx
# CORS Headers
add_header 'Access-Control-Allow-Origin' '*' always;
add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;

# Security Headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;

# Client Max Body Size for file uploads
client_max_body_size 100M;
```

## 🎯 Kết quả sau khi setup:

✅ `https://pay.mtfgame.com` → SSL enabled, auto redirect HTTP → HTTPS
✅ `https://id.mtfgame.com` → SSL enabled, auto redirect HTTP → HTTPS
✅ Automatic SSL certificate renewal
✅ Security headers enabled
✅ CORS configured properly

## 🔍 Troubleshooting:

### Nếu không connect được:
1. Kiểm tra project hiện tại đã chạy trên port 8080
2. Kiểm tra firewall/antivirus
3. Thử dùng IP thay vì `host.docker.internal`:
   - Windows: `host.docker.internal`
   - Linux: `**********` hoặc `$(docker network inspect bridge | grep Gateway | awk '{print $2}' | tr -d '",')`

### Nếu SSL không hoạt động:
1. Kiểm tra domains đã point đúng IP server
2. Kiểm tra port 80, 443 đã mở
3. Kiểm tra email hợp lệ cho Let's Encrypt
