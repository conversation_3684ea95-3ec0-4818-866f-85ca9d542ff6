<?php $this->startSection('content'); ?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Page Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
            <i class="fas fa-history text-rose-600 mr-3"></i>
            <?= $__('history.view.title') ?>
        </h1>
        <p class="text-xl text-gray-600"><?= $__('history.view.subtitle') ?></p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-plus text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900"><?= $this->currency($summary['total_deposits'], 'medium') ?></div>
                    <div class="text-sm text-gray-600"><?= $__('history.view.total_deposits') ?></div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-minus text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900"><?= $this->currency($summary['total_spent'], 'medium') ?></div>
                    <div class="text-sm text-gray-600"><?= $__('history.view.total_spent') ?></div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-rose-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-wallet text-rose-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900"><?= $this->currency($summary['current_balance'], 'medium') ?></div>
                    <div class="text-sm text-gray-600"><?= $__('history.view.current_balance') ?></div>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900"><?= $summary['transaction_count']['pending'] ?? 0 ?></div>
                    <div class="text-sm text-gray-600"><?= $__('history.view.pending') ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
        <form id="filterForm" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                    <option value="all" <?= $filters['type'] === 'all' ? 'selected' : '' ?>><?= $__('history.view.all_types') ?></option>
                    <option value="deposit" <?= $filters['type'] === 'deposit' ? 'selected' : '' ?>><?= $__('history.view.deposit') ?></option>
                    <option value="purchase" <?= $filters['type'] === 'purchase' ? 'selected' : '' ?>><?= $__('history.view.purchase') ?></option>
                </select>
            </div>

            <div>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500">
                    <option value="all" <?= $filters['status'] === 'all' ? 'selected' : '' ?>><?= $__('history.view.all_status') ?></option>
                    <option value="completed" <?= $filters['status'] === 'completed' ? 'selected' : '' ?>><?= $__('history.view.completed') ?></option>
                    <option value="pending" <?= $filters['status'] === 'pending' ? 'selected' : '' ?>><?= $__('history.view.pending') ?></option>
                    <option value="failed" <?= $filters['status'] === 'failed' ? 'selected' : '' ?>><?= $__('history.view.failed') ?></option>
                </select>
            </div>

            <div>
                <input type="date" name="date_from" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="<?= $filters['date_from'] ?>" placeholder="<?= $__('history.view.from_date') ?>">
            </div>

            <div>
                <input type="date" name="date_to" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="<?= $filters['date_to'] ?>" placeholder="<?= $__('history.view.to_date') ?>">
            </div>

            <div>
                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2 px-4 rounded-lg font-semibold hover:from-emerald-600 hover:to-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    <?= $__('history.view.filter') ?>
                </button>
            </div>
        </form>
    </div>

    <!-- Transactions Table -->
    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.transaction_id') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.transaction_type') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.amount') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.payment_method') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.transaction_status') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.transaction_date') ?></th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $__('history.view.balance_after') ?></th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($transactions['data'] as $transaction): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?= $this->escape($transaction['id']) ?></td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <?= $transaction['type'] === 'deposit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                <i class="fas fa-<?= $transaction['type'] === 'deposit' ? 'plus' : 'minus' ?> mr-1"></i>
                                <?= $transaction['type'] === 'deposit' ? 'Nạp tiền' : 'Mua hàng' ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold <?= $transaction['amount'] > 0 ? 'text-green-600' : 'text-red-600' ?>">
                            <?= $this->currency(abs($transaction['amount'])) ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= $this->escape($transaction['payment_method'] ?? 'N/A') ?></td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                <?php if ($transaction['status'] === 'completed'): ?>
                                    bg-green-100 text-green-800
                                <?php elseif ($transaction['status'] === 'pending'): ?>
                                    bg-yellow-100 text-yellow-800
                                <?php else: ?>
                                    bg-red-100 text-red-800
                                <?php endif; ?>">
                                <?= ucfirst($transaction['status']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?= $this->date($transaction['created_at']) ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            <?= $this->currency($transaction['balance_after'] ?? 0) ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($transactions['total_pages'] > 1): ?>
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div class="text-sm text-gray-700">
                Hiển thị <?= count($transactions['data']) ?> / <?= $transactions['total'] ?> giao dịch
            </div>
            <div class="flex space-x-2">
                <?php for ($i = 1; $i <= $transactions['total_pages']; $i++): ?>
                    <a href="?page=<?= $i ?>&type=<?= $filters['type'] ?>&status=<?= $filters['status'] ?>"
                       class="px-3 py-2 text-sm font-medium rounded-lg transition-colors <?= $i === $transactions['current_page'] ? 'bg-rose-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' ?>">
                        <?= $i ?>
                    </a>
                <?php endfor; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Filter form submission
    $('#filterForm').submit(function(e) {
        e.preventDefault();

        const formData = $(this).serialize();
        window.location.href = '<?= $this->url('/history') ?>?' + formData;
    });
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
