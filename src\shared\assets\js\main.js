/**
 * Main JavaScript file for Gaming Account Manager
 */

// Global variables (domains and API URLs are set from PHP in layout)
window.GamingApp = window.GamingApp || {
    config: {
        domains: {},
        api: {},
        notificationDuration: 5000
    },
    user: null,
    notifications: []
};

// Remove the early initialization - will be moved to the end

/**
 * Application initialization
 */
GamingApp.init = function() {
    console.log('Gaming App initialized');

    // Initialize components
    this.initNotifications();
    this.initFormValidation();
    this.initTooltips();
    this.initAnimations();
    this.initBalanceRefresh();

    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
};

/**
 * Initialize notification system
 */
GamingApp.initNotifications = function() {
    // Create notification container if it doesn't exist
    if ($('#notification-container').length === 0) {
        $('body').append('<div id="notification-container" class="notification-container"></div>');
    }
};

/**
 * Show modern floating notification
 */
GamingApp.showNotification = function(message, type = 'info', duration = 4000) {
    // Create notification container if it doesn't exist
    if ($('#gaming-toast-container').length === 0) {
        $('body').append(`
            <div id="gaming-toast-container" class="fixed top-4 right-4 z-50 space-y-3" style="z-index: 9999;">
            </div>
        `);
    }

    // Generate unique ID for this notification
    const notificationId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

    // Get icon and colors based on type
    const config = this.getNotificationConfig(type);

    // Create modern toast HTML with enhanced visibility
    const toastHtml = `
        <div id="${notificationId}" class="gaming-toast transform translate-x-full opacity-0 transition-all duration-500 ease-out">
            <div class="bg-white rounded-xl shadow-2xl border-l-4 ${config.borderColor} p-4 min-w-80 max-w-sm backdrop-blur-sm ring-1 ring-black/5">
                <div class="flex items-start space-x-3">
                    <!-- Enhanced icon with glow effect -->
                    <div class="flex-shrink-0 relative">
                        <div class="w-12 h-12 rounded-xl ${config.bgGradient} flex items-center justify-center shadow-lg ${config.glowEffect}">
                            <i class="${config.icon} text-white text-xl drop-shadow-sm"></i>
                        </div>
                        <!-- Enhanced animations for all types -->
                        ${type === 'success' ? '<div class="absolute inset-0 rounded-xl bg-green-400 animate-ping opacity-30"></div>' : ''}
                        ${type === 'error' ? '<div class="absolute inset-0 rounded-xl bg-red-400 animate-ping opacity-25"></div>' : ''}
                        ${type === 'warning' ? '<div class="absolute inset-0 rounded-xl bg-amber-400 animate-pulse opacity-25"></div>' : ''}
                        ${type === 'info' ? '<div class="absolute inset-0 rounded-xl bg-blue-400 animate-bounce opacity-20"></div>' : ''}
                    </div>

                    <!-- Content with better typography -->
                    <div class="flex-1 min-w-0">
                        <p class="text-gray-900 text-sm font-semibold leading-5 pr-2">${message}</p>
                    </div>

                    <!-- Enhanced close button -->
                    <button onclick="GamingApp.closeNotification('${notificationId}')"
                            class="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-all duration-200 p-1.5 hover:bg-gray-100 rounded-lg hover:scale-110">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- Enhanced progress bar for all types -->
                ${duration > 0 ? `
                    <div class="mt-3 h-1.5 bg-gray-100 rounded-full overflow-hidden shadow-inner">
                        <div class="h-full ${config.progressGradient} rounded-full shadow-sm"
                             style="width: 100%; animation: progress-${notificationId} ${duration}ms linear forwards;"></div>
                    </div>
                    <style>
                        @keyframes progress-${notificationId} {
                            from { width: 100%; }
                            to { width: 0%; }
                        }
                    </style>
                ` : ''}
            </div>
        </div>
    `;

    // Add to container
    $('#gaming-toast-container').append(toastHtml);


    // Animate in
    setTimeout(() => {
        $(`#${notificationId}`).removeClass('translate-x-full opacity-0').addClass('translate-x-0 opacity-100');
    }, 10);

    // Auto remove after duration (all types now auto-close)
    if (duration > 0) {
        setTimeout(() => {
            this.closeNotification(notificationId);
        }, duration);
    }

    return notificationId;
};

/**
 * Get notification configuration based on type
 */
GamingApp.getNotificationConfig = function(type) {
    const configs = {
        success: {
            icon: 'fas fa-check',
            bgGradient: 'bg-gradient-to-br from-green-400 to-green-600',
            borderColor: 'border-green-500',
            progressGradient: 'bg-gradient-to-r from-green-400 to-green-600',
            glowEffect: 'shadow-green-500/25'
        },
        error: {
            icon: 'fas fa-exclamation-circle',
            bgGradient: 'bg-gradient-to-br from-red-400 to-red-600',
            borderColor: 'border-red-500',
            progressGradient: 'bg-gradient-to-r from-red-400 to-red-600',
            glowEffect: 'shadow-red-500/25'
        },
        warning: {
            icon: 'fas fa-exclamation-triangle',
            bgGradient: 'bg-gradient-to-br from-amber-400 to-orange-500',
            borderColor: 'border-amber-500',
            progressGradient: 'bg-gradient-to-r from-amber-400 to-orange-500',
            glowEffect: 'shadow-amber-500/25'
        },
        info: {
            icon: 'fas fa-info-circle',
            bgGradient: 'bg-gradient-to-br from-blue-400 to-blue-600',
            borderColor: 'border-blue-500',
            progressGradient: 'bg-gradient-to-r from-blue-400 to-blue-600',
            glowEffect: 'shadow-blue-500/25'
        }
    };
    return configs[type] || configs.info;
};

/**
 * Close notification with smooth slide-out animation
 */
GamingApp.closeNotification = function(id) {
    const $toast = $(`#${id}`);
    if ($toast.length) {
        // Slide out to the right
        $toast.removeClass('translate-x-0 opacity-100').addClass('translate-x-full opacity-0');

        setTimeout(() => {
            $toast.remove();

            // Remove container if no more toasts
            if ($('#gaming-toast-container .gaming-toast').length === 0) {
                $('#gaming-toast-container').remove();
            }
        }, 500);
    }
};



/**
 * Initialize form validation
 */
GamingApp.initFormValidation = function() {
    // Only validate on blur (when user finishes with field) - less spam
    $(document).on('blur', 'input[type="text"], input[type="email"], input[type="password"], input[type="tel"]', function() {
        const $input = $(this);
        GamingApp.validateInput($input);

        // Cross-validate password fields
        const name = $input.attr('name');
        if (name === 'password' || name === 'new_password') {
            const $confirmPassword = $('input[name="confirm_password"]');
            if ($confirmPassword.length && $confirmPassword.val()) {
                GamingApp.validateInput($confirmPassword);
            }
        } else if (name === 'confirm_password') {
            const $password = $('input[name="password"]');
            const $newPassword = $('input[name="new_password"]');
            if ($password.length && $password.val()) {
                GamingApp.validateInput($password);
            } else if ($newPassword.length && $newPassword.val()) {
                GamingApp.validateInput($newPassword);
            }
        }
    });

    // Clear validation styling on focus (clean slate when user starts typing)
    $(document).on('focus', 'input[type="text"], input[type="email"], input[type="password"], input[type="tel"]', function() {
        const $input = $(this);
        $input.removeClass('border-red-500 border-green-500').addClass('border-blue-500');
        $input.siblings('.validation-message').remove();
        $input.parent().find('.validation-icon').remove();
    });

    // Validate on paste (immediate)
    $(document).on('paste', 'input', function() {
        const $input = $(this);
        setTimeout(() => {
            GamingApp.validateInput($input);
        }, 100);
    });
};

/**
 * Validate individual input field
 */
GamingApp.validateInput = function($input) {
    const originalValue = $input.val();
    const value = originalValue.trim();
    const name = $input.attr('name');
    const type = $input.attr('type');

    // Auto-trim username field
    if (name === 'username' && originalValue !== value) {
        $input.val(value);
    }

    // Clear previous validation
    $input.removeClass('border-red-500 border-green-500 border-gray-300');
    $input.siblings('.validation-message').remove();
    $input.parent().find('.validation-icon').remove();

    // Skip validation if field is empty and not required
    if (!value && !$input.prop('required')) {
        $input.addClass('border-gray-300');
        return true;
    }

    let isValid = true;
    let errorMessage = '';

    // Required field validation
    if ($input.prop('required') && !value) {
        isValid = false;
        errorMessage = window.translations?.validation?.required || 'Trường này là bắt buộc';
    }
    // Email validation
    else if ((name === 'email' || type === 'email') && value && !GamingApp.isValidEmail(value)) {
        isValid = false;
        errorMessage = window.translations?.validation?.email_invalid || 'Email không hợp lệ (ví dụ: <EMAIL>)';
    }
    // Phone validation
    else if ((name === 'phone' || type === 'tel') && value && !GamingApp.isValidPhone(value)) {
        isValid = false;
        errorMessage = window.translations?.validation?.phone_invalid || 'Số điện thoại phải có 10 số và bắt đầu bằng 0';
    }
    // Username validation
    else if (name === 'username' && value) {
        // Check for whitespace in original value
        if (originalValue.includes(' ')) {
            isValid = false;
            errorMessage = window.translations?.validation?.username_no_spaces || 'Tên đăng nhập không được chứa khoảng trắng';
        } else if (value.length < 6) {
            isValid = false;
            errorMessage = window.translations?.validation?.username_min || 'Tên đăng nhập phải có ít nhất 6 ký tự';
        } else if (value.length > 20) {
            isValid = false;
            errorMessage = window.translations?.validation?.username_max || 'Tên đăng nhập không được quá 20 ký tự';
        } else if (!GamingApp.isValidUsername(value)) {
            isValid = false;
            errorMessage = window.translations?.validation?.username_format || 'Chỉ được chứa chữ cái, số và dấu gạch dưới';
        }
    }
    // Password validation (both 'password' and 'new_password')
    else if ((name === 'password' || name === 'new_password') && value) {
        if (value.length < 6) {
            isValid = false;
            errorMessage = window.translations?.validation?.password_min || 'Mật khẩu phải có ít nhất 6 ký tự';
        } else if (value.length > 30) {
            isValid = false;
            errorMessage = window.translations?.validation?.password_max || 'Mật khẩu không được quá 30 ký tự';
        }
        // No password strength evaluation - just green border when valid
    }
    // Confirm password validation
    else if (name === 'confirm_password' && value) {
        // Check for both 'password' (register form) and 'new_password' (change password form)
        const password = $('input[name="password"]').val() || $('input[name="new_password"]').val();
        if (!password) {
            isValid = false;
            errorMessage = window.translations?.validation?.password_enter_first || 'Vui lòng nhập mật khẩu trước';
        } else if (value !== password) {
            isValid = false;
            errorMessage = window.translations?.validation?.password_mismatch || 'Mật khẩu xác nhận không khớp';
        }
        // No success message for matching password - just green border is enough
    }

    // Apply validation result
    if (isValid && value) {
        $input.addClass('border-green-500');
        // No success messages needed - just green border and icon is enough
        // Add success icon
        if ($input.parent().hasClass('relative')) {
            $input.parent().append('<div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500"><i class="fas fa-check"></i></div>');
        }
    } else if (!isValid) {
        $input.addClass('border-red-500');
        if (errorMessage) {
            $input.after(`<div class="validation-message text-red-600 text-sm mt-1 flex items-center">
                <i class="fas fa-exclamation-circle mr-1"></i>${errorMessage}
            </div>`);
        }
        // Add error icon
        if ($input.parent().hasClass('relative')) {
            $input.parent().append('<div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500"><i class="fas fa-times"></i></div>');
        }
    } else {
        $input.addClass('border-gray-300');
    }

    return isValid;
};

/**
 * Validation helper functions
 */
GamingApp.isValidEmail = function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

GamingApp.isValidPhone = function(phone) {
    const phoneRegex = /^0[0-9]{9}$/;
    return phoneRegex.test(phone);
};

GamingApp.isValidUsername = function(username) {
    // Check for whitespace first
    if (username.includes(' ')) {
        return false;
    }
    // Check format: only letters, numbers, and underscores, 6-20 characters
    const usernameRegex = /^[a-zA-Z0-9_]{6,20}$/;
    return usernameRegex.test(username);
};

GamingApp.isValidPassword = function(password) {
    return password.length >= 6 && password.length <= 30;
};

/**
 * Get password strength
 */
GamingApp.getPasswordStrength = function(password) {
    let score = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) score += 1;
    else feedback.push(window.translations?.password_strength?.min_8_chars || 'ít nhất 8 ký tự');

    // Uppercase check
    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push(window.translations?.password_strength?.uppercase || 'chữ hoa');

    // Lowercase check
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push(window.translations?.password_strength?.lowercase || 'chữ thường');

    // Number check
    if (/[0-9]/.test(password)) score += 1;
    else feedback.push(window.translations?.password_strength?.number || 'số');

    // Special character check
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    else feedback.push(window.translations?.password_strength?.special_char || 'ký tự đặc biệt');

    let level = '';
    if (score <= 2) level = window.translations?.password_strength?.weak || 'yếu';
    else if (score <= 3) level = window.translations?.password_strength?.medium || 'trung bình';
    else if (score <= 4) level = window.translations?.password_strength?.strong || 'mạnh';
    else level = window.translations?.password_strength?.very_strong || 'rất mạnh';

    return {
        score: score,
        level: level,
        feedback: feedback.length > 0 ? (window.translations?.password_strength?.need_more || 'cần thêm') + ' ' + feedback.join(', ') : (window.translations?.password_strength?.good || 'tốt')
    };
};

/**
 * Initialize tooltips
 */
GamingApp.initTooltips = function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
};

/**
 * Initialize animations
 */
GamingApp.initAnimations = function() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements with animation class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
};

/**
 * Initialize balance refresh functionality
 */
GamingApp.initBalanceRefresh = function() {
    const balanceElement = document.getElementById('userBalance');
    const refreshBtn = document.getElementById('refreshBalanceBtn');

    if (!balanceElement || !refreshBtn) return;

    // Rate limiting - 3 seconds between clicks
    let lastRefreshTime = 0;
    const REFRESH_COOLDOWN = 3000; // 3 seconds

    // // Refresh balance every 30 seconds
    // setInterval(() => {
    //     this.refreshUserBalance();
    // }, 30000); // 30 seconds
    //this.refreshUserBalance();

    // Add click to refresh functionality with rate limiting
    refreshBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime;

        if (timeSinceLastRefresh < REFRESH_COOLDOWN) {
            const remainingTime = Math.ceil((REFRESH_COOLDOWN - timeSinceLastRefresh) / 1000);
            const message = (window.translations?.balance?.wait_seconds || 'Vui lòng đợi {seconds} giây trước khi làm mới lại').replace('{seconds}', remainingTime);
            this.showNotification(message, 'warning');
            return;
        }

        lastRefreshTime = now;
        this.refreshUserBalance(true);
    });
};

/**
 * Refresh user balance
 */
GamingApp.refreshUserBalance = function(showLoading = false) {
    const balanceElement = document.getElementById('userBalance');
    const refreshBtn = document.getElementById('refreshBalanceBtn');

    if (!balanceElement) return;

    if (showLoading && refreshBtn) {
        // Animate refresh button
        refreshBtn.classList.add('animate-spin');
        refreshBtn.disabled = true;
        refreshBtn.style.color = '#e11d48'; // Rose color during loading
    }

    $.ajax({
        url: GamingApp.config.api.refreshBalance,
        method: 'POST',
        dataType: 'json',
        xhrFields: {
            withCredentials: true
        },
        success: (response) => {
            if (response.status) {
                balanceElement.innerHTML = response.data.formatted_balance + ' <img src="/assets/images/coin_small.png" alt="Coin" class="inline-block w-4 h-4 ml-1" style="vertical-align: middle;">';

                if (showLoading) {
                    // Show success animation
                    balanceElement.style.color = '#10b981'; // Green
                    if (refreshBtn) {
                        refreshBtn.style.color = '#10b981'; // Green
                    }

                    setTimeout(() => {
                        balanceElement.style.color = '#e11d48'; // Back to rose
                        if (refreshBtn) {
                            refreshBtn.style.color = '#9ca3af'; // Back to gray
                        }
                    }, 1000);

                    this.showNotification(window.translations?.balance?.updated || 'Số dư đã được cập nhật', 'success');
                }
            } else {
                console.error('Failed to refresh balance:', response.message);
                if (showLoading) {
                    balanceElement.style.color = '#ef4444'; // Red
                    if (refreshBtn) {
                        refreshBtn.style.color = '#ef4444'; // Red
                    }

                    setTimeout(() => {
                        balanceElement.style.color = '#e11d48'; // Back to rose
                        if (refreshBtn) {
                            refreshBtn.style.color = '#9ca3af'; // Back to gray
                        }
                    }, 2000);

                    this.showNotification(window.translations?.balance?.update_failed || 'Không thể cập nhật số dư', 'error');
                }
            }
        },
        error: (xhr, status, error) => {
            console.error('Balance refresh error:', error);
            if (showLoading) {
                balanceElement.style.color = '#ef4444'; // Red
                if (refreshBtn) {
                    refreshBtn.style.color = '#ef4444'; // Red
                }

                setTimeout(() => {
                    balanceElement.style.color = '#e11d48'; // Back to rose
                    if (refreshBtn) {
                        refreshBtn.style.color = '#9ca3af'; // Back to gray
                    }
                }, 2000);

                this.showNotification(window.translations?.balance?.connection_error || 'Lỗi kết nối', 'error');
            }
        },
        complete: () => {
            if (showLoading && refreshBtn) {
                // Stop animation and re-enable button
                refreshBtn.classList.remove('animate-spin');
                refreshBtn.disabled = false;
            }
        }
    });
};

/**
 * API helper functions
 */
GamingApp.api = {
    /**
     * Make API request
     */
    request: function(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        // Add CSRF token if available
        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        if (csrfToken) {
            defaultOptions.headers['X-CSRF-TOKEN'] = csrfToken;
        }

        // Add auth token if available
        const authToken = localStorage.getItem('auth_token');
        if (authToken) {
            defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
        }

        const finalOptions = { ...defaultOptions, ...options };

        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API request failed:', error);
                throw error;
            });
    },

    /**
     * GET request
     */
    get: function(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = url + (Object.keys(params).length ? '?' + urlParams : '');
        return this.request(fullUrl);
    },

    /**
     * POST request
     */
    post: function(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
};

/**
 * Utility functions
 */
GamingApp.utils = {
    /**
     * Format currency with coin icon
     */
    formatCurrency: function(amount, size = 'small') {
        const formatted = new Intl.NumberFormat('vi-VN').format(amount);
        const coinIcon = size === 'big' ? 'coin_big.png' : 'coin_small.png';
        const iconSize = size === 'big' ? 'w-6 h-6' : size === 'medium' ? 'w-5 h-5' : size === 'tiny' ? 'w-3 h-3' : 'w-4 h-4';
        return formatted + ' <img src="/assets/images/' + coinIcon + '" alt="Coin" class="inline-block ' + iconSize + ' ml-1" style="vertical-align: middle;">';
    },

    /**
     * Format date
     */
    formatDate: function(date, format = 'dd/MM/yyyy HH:mm') {
        const d = new Date(date);
        return d.toLocaleDateString('vi-VN') + ' ' + d.toLocaleTimeString('vi-VN');
    },

    /**
     * Debounce function
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Copy to clipboard
     */
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            GamingApp.showNotification('Đã sao chép vào clipboard', 'success');
        }).catch(() => {
            GamingApp.showNotification('Không thể sao chép', 'error');
        });
    }
};

/**
 * Gaming-specific effects
 */
GamingApp.effects = {
    /**
     * Add particle effect to element
     */
    addParticleEffect: function(element) {
        // Create particle container
        const particles = document.createElement('div');
        particles.className = 'particle-effect';
        element.appendChild(particles);

        // Create particles
        for (let i = 0; i < 10; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particles.appendChild(particle);
        }

        // Remove after animation
        setTimeout(() => {
            particles.remove();
        }, 3000);
    },

    /**
     * Screen shake effect
     */
    screenShake: function(duration = 500) {
        document.body.classList.add('screen-shake');
        setTimeout(() => {
            document.body.classList.remove('screen-shake');
        }, duration);
    }
};

// Export for global use
window.GamingApp = GamingApp;

// Initialize the application when DOM is ready
$(document).ready(function() {
    GamingApp.init();
});
