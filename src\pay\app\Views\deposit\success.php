<?php $this->startSection('content'); ?>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
    <div class="max-w-2xl mx-auto px-4">
        <!-- Success Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 sm:p-8 text-center">
            <!-- Success Icon -->
            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-check-circle text-4xl text-green-600"></i>
            </div>

            <!-- Success Message -->
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 mb-4">
                Thanh toán thành công!
            </h1>
            <p class="text-gray-600 mb-8">
                Giao dịch của bạn đã được x<PERSON> lý thành công. Số dư tài khoản sẽ đư<PERSON><PERSON> cập nhật trong vài phút.
            </p>

            <!-- Order Information -->
            <?php if (isset($order_data)): ?>
            <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h3 class="font-semibold text-gray-900 mb-3">Thông tin giao dịch:</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Mã đơn hàng:</span>
                        <span class="font-medium"><?= $this->escape($order_data['order_code'] ?? '') ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Số tiền:</span>
                        <span class="font-medium text-green-600"><?= number_format($order_data['total_amount'] ?? 0) ?> VNĐ</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Phương thức:</span>
                        <span class="font-medium"><?= $this->escape($order_data['payment_method'] ?? '') ?></span>
                    </div>
                    <?php if (!empty($order_data['bank_code'])): ?>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Ngân hàng:</span>
                        <span class="font-medium"><?= $this->escape($order_data['bank_code']) ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Trạng thái:</span>
                        <span class="font-medium text-green-600">Thành công</span>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <a href="<?= $this->url('/') ?>" class="flex-1 bg-gradient-to-r from-emerald-500 to-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all">
                    <i class="fas fa-home mr-2"></i>
                    Về trang chủ
                </a>
                <a href="<?= $this->url('/history') ?>" class="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-all">
                    <i class="fas fa-history mr-2"></i>
                    Lịch sử giao dịch
                </a>
            </div>

            <!-- Additional Info -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-info-circle text-blue-600 mt-0.5"></i>
                    <div class="text-left">
                        <p class="text-sm text-blue-800 font-medium mb-1">Lưu ý quan trọng:</p>
                        <ul class="text-xs text-blue-700 space-y-1">
                            <li>• Số dư sẽ được cập nhật tự động trong 5-10 phút</li>
                            <li>• Nếu sau 30 phút chưa nhận được tiền, vui lòng liên hệ hỗ trợ</li>
                            <li>• Lưu lại mã đơn hàng để tra cứu khi cần thiết</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
