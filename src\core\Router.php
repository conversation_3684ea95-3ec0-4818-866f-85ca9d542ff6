<?php

namespace Core;

/**
 * Router Class
 * Handles URL routing and middleware
 */
class Router
{
    private array $routes = [];
    private array $middlewares = [];
    private array $groupStack = [];

    /**
     * Add GET route
     */
    public function get(string $uri, $action): void
    {
        $this->addRoute('GET', $uri, $action);
    }

    /**
     * Add POST route
     */
    public function post(string $uri, $action): void
    {
        $this->addRoute('POST', $uri, $action);
    }

    /**
     * Add PUT route
     */
    public function put(string $uri, $action): void
    {
        $this->addRoute('PUT', $uri, $action);
    }

    /**
     * Add DELETE route
     */
    public function delete(string $uri, $action): void
    {
        $this->addRoute('DELETE', $uri, $action);
    }

    /**
     * Group routes with common attributes
     */
    public function group(array $attributes, callable $callback): void
    {
        $this->groupStack[] = $attributes;
        $callback($this);
        array_pop($this->groupStack);
    }

    /**
     * Add route to collection
     */
    private function addRoute(string $method, string $uri, $action): void
    {
        $groupAttributes = $this->getGroupAttributes();

        // Apply group prefix
        if (isset($groupAttributes['prefix'])) {
            $uri = '/' . trim($groupAttributes['prefix'], '/') . '/' . trim($uri, '/');
        }

        // Apply group middleware
        $middleware = $groupAttributes['middleware'] ?? [];
        if (!is_array($middleware)) {
            $middleware = [$middleware];
        }

        $this->routes[] = [
            'method' => $method,
            'uri' => $this->normalizeUri($uri),
            'action' => $action,
            'middleware' => $middleware
        ];
    }

    /**
     * Get current group attributes
     */
    private function getGroupAttributes(): array
    {
        $attributes = [];

        foreach ($this->groupStack as $group) {
            $attributes = array_merge($attributes, $group);
        }

        return $attributes;
    }

    /**
     * Normalize URI
     */
    private function normalizeUri(string $uri): string
    {
        return '/' . trim($uri, '/');
    }

    /**
     * Handle incoming request
     */
    public function handleRequest(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = $this->getCurrentUri();

        $route = $this->findRoute($method, $uri);

        if (!$route) {
            $this->handleNotFound();
            return;
        }

        // Run middleware
        if (!$this->runMiddleware($route['middleware'])) {
            return;
        }

        // Execute controller action
        $this->executeAction($route['action'], $route['parameters'] ?? []);
    }

    /**
     * Get current URI
     */
    private function getCurrentUri(): string
    {
        $uri = $_SERVER['REQUEST_URI'];

        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }

        // Remove script name from URI if present
        $scriptName = dirname($_SERVER['SCRIPT_NAME']);
        if ($scriptName !== '/' && str_starts_with($uri, $scriptName)) {
            $uri = substr($uri, strlen($scriptName));
        }

        return $this->normalizeUri($uri);
    }

    /**
     * Find matching route
     */
    private function findRoute(string $method, string $uri): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] === $method) {
                $match = $this->matchUri($route['uri'], $uri);
                if ($match !== false) {
                    $route['parameters'] = $match;
                    return $route;
                }
            }
        }

        return null;
    }

    /**
     * Check if URI matches route pattern and extract parameters
     */
    private function matchUri(string $pattern, string $uri)
    {
        // Convert route pattern to regex
        $regex = preg_replace('/\{([^}]+)\}/', '([^/]+)', $pattern);
        $regex = '#^' . $regex . '$#';

        if (preg_match($regex, $uri, $matches)) {
            // Remove the full match
            array_shift($matches);

            // Extract parameter names from pattern
            preg_match_all('/\{([^}]+)\}/', $pattern, $paramNames);
            $paramNames = $paramNames[1];

            // Combine parameter names with values
            $parameters = [];
            foreach ($paramNames as $index => $name) {
                $parameters[$name] = $matches[$index] ?? null;
            }

            return $parameters;
        }

        return false;
    }

    /**
     * Run middleware stack
     */
    private function runMiddleware(array $middlewares): bool
    {
        foreach ($middlewares as $middleware) {
            $middlewareClass = "Shared\\Middleware\\" . ucfirst($middleware) . "Middleware";

            if (!class_exists($middlewareClass)) {
                throw new \Exception("Middleware {$middlewareClass} not found");
            }

            $middlewareInstance = new $middlewareClass();

            if (!$middlewareInstance->handle()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Execute controller action
     */
    private function executeAction($action, array $parameters = []): void
    {
        if (is_string($action)) {
            [$controller, $method] = explode('@', $action);

            // Check if controller already has full namespace
            if (strpos($controller, '\\') !== false) {
                $controllerClass = $controller;
            } else {
                // Default to App\Controllers for backward compatibility
                $controllerClass = "App\\Controllers\\" . $controller;
            }

            if (!class_exists($controllerClass)) {
                throw new \Exception("Controller {$controllerClass} not found");
            }

            $controllerInstance = new $controllerClass();

            if (!method_exists($controllerInstance, $method)) {
                throw new \Exception("Method {$method} not found in {$controllerClass}");
            }

            // Call method with parameters
            if (!empty($parameters)) {
                $controllerInstance->$method(...array_values($parameters));
            } else {
                $controllerInstance->$method();
            }
        } elseif (is_callable($action)) {
            $action();
        } else {
            throw new \Exception("Invalid action type");
        }
    }

    /**
     * Handle 404 Not Found
     */
    private function handleNotFound(): void
    {
        http_response_code(404);

        // Initialize language helper for error pages
        $app = Application::getInstance();
        $config = $app->getConfig();
        $currentLanguage = $_SESSION['language'] ?? $config['app']['locale'] ?? 'vi';
        $languageHelper = \Shared\Helpers\LanguageHelper::getInstance($currentLanguage);

        $view = new View();
        $view->render('errors/404', [
            'title' => $languageHelper->translate('shared.errors.page_not_found'),
            'message' => $languageHelper->translate('shared.errors.page_not_found_message'),
            'currentLanguage' => $currentLanguage,
            '__' => function($key, $params = []) use ($languageHelper) {
                return $languageHelper->translate($key, $params);
            }
        ]);
    }

    /**
     * Generate URL for named route
     */
    public function url(string $name, array $parameters = []): string
    {
        // Implementation for named routes
        // For now, return simple URL
        $app = Application::getInstance();
        $baseUrl = rtrim($app->getBaseUrl(), '/');
        return $baseUrl . '/' . ltrim($name, '/');
    }
}
