<?php $this->startSection('content'); ?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-12">
    <!-- Header Section -->
    <div class="text-center mb-8 sm:mb-16">
        <h1 class="text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4 sm:mb-6">
            <?= $__('home.view.select_product') ?>
        </h1>
        <p class="text-base sm:text-lg lg:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed px-4">
            <?= $__('home.view.explore_games') ?>
        </p>
        <div class="mt-4 sm:mt-6 w-16 sm:w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
    </div>

    <!-- Games Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto">
        <?php foreach ($games as $index => $game): ?>
        <div class="game-item group relative bg-white rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer transform hover:-translate-y-2 flex flex-col" data-game-id="<?= $game['id'] ?>">

            <!-- Game Image -->
            <div class="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 mx-auto mb-4 sm:mb-6 rounded-2xl sm:rounded-3xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300">
                <img src="<?= $this->asset('images/' . $game['image']) ?>" alt="<?= $this->escape($game['name']) ?>" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
            </div>

            <!-- Game Info -->
            <div class="text-center flex-grow flex flex-col justify-between">
                <div>
                    <h3 class="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-2 sm:mb-4 group-hover:text-blue-600 transition-colors duration-300">
                        <?= $this->escape($game['name']) ?>
                    </h3>

                    <!-- Game description -->
                    <div class="text-gray-600 mb-4 sm:mb-6 lg:mb-8 leading-relaxed min-h-[40px] sm:min-h-[60px] flex items-center justify-center">
                        <?php if ($game['id'] === 'lua_game'): ?>
                            <div class="space-y-1 sm:space-y-2">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-coins text-yellow-500 mr-2 text-sm sm:text-base"></i>
                                    <span class="font-semibold text-sm sm:text-base">Tiền tệ trong game</span>
                                </div>
                                <p class="text-xs sm:text-sm">Nạp lúa để sử dụng trong tất cả các game</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-1 sm:space-y-2">
                                <div class="flex items-center justify-center">
                                    <i class="fas fa-gamepad text-purple-500 mr-2 text-sm sm:text-base"></i>
                                    <span class="font-semibold text-sm sm:text-base">Game phiêu lưu</span>
                                </div>
                                <p class="text-xs sm:text-sm">Khám phá thế giới mới đầy thú vị</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Action Button -->
                <div class="action-button bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 sm:py-4 px-4 sm:px-6 lg:px-8 rounded-lg sm:rounded-xl font-semibold group-hover:from-blue-600 group-hover:to-purple-700 transition-all duration-300 inline-block w-full text-sm sm:text-base">
                    <i class="fas fa-play mr-2 text-xs sm:text-sm"></i>
                    <?= $game['id'] === 'lua_game' ? 'Nạp Lúa Ngay' : 'Chọn Game' ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Handle game selection - click on entire box
    $('.game-item').click(function() {
        const gameId = $(this).data('game-id');

        // Store selected game in session storage
        sessionStorage.setItem('selectedGame', gameId);

        // Check if user is logged in and redirect immediately
        <?php if ($this->isAuth()): ?>
            // User is logged in, redirect based on game type
            if (gameId === 'lua_game') {
                // Nạp Lúa -> go to deposit page
                window.location.href = '<?= $this->url('/deposit') ?>';
            } else {
                // Other games -> go to game guide page
                window.location.href = '<?= $this->url('/game/') ?>' + gameId + '/guide';
            }
        <?php else: ?>
            // User not logged in, redirect to id domain for login with return URL
            let returnPath;
            if (gameId === 'lua_game') {
                returnPath = '/deposit';
            } else {
                returnPath = '/game/' + gameId + '/guide';
            }
            const returnUrl = encodeURIComponent(window.location.origin + returnPath);
            window.location.href = GamingApp.config.domains.id + '/?return=' + returnUrl;
        <?php endif; ?>
    });

    // Enhanced hover effects with smooth animations
    $('.game-item').hover(
        function() {
            $(this).find('img').addClass('brightness-110');
            $(this).find('.action-button').addClass('shadow-lg scale-105');
        },
        function() {
            $(this).find('img').removeClass('brightness-110');
            $(this).find('.action-button').removeClass('shadow-lg scale-105');
        }
    );
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
