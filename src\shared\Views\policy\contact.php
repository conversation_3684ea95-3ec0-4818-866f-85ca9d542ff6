<?php $this->startSection('content'); ?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
    <!-- Header -->
    <div class="text-center mb-8 sm:mb-10 lg:mb-12">
        <h1 class="text-2xl sm:text-3xl lg:text-4xl font-gaming font-bold text-gray-900 mb-3 sm:mb-4"><?= $__('policy.contact.view.title') ?></h1>
        <p class="text-base sm:text-lg text-gray-600"><?= $__('policy.contact.view.subtitle') ?></p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
        <!-- Contact Form -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200 p-4 sm:p-6 lg:p-8">
            <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('policy.contact.view.form_title') ?></h2>

            <form id="contactForm" class="space-y-4 sm:space-y-6">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                <!-- Name -->
                <div>
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-user mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('policy.contact.view.name') ?>
                    </label>
                    <input type="text" name="name" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('policy.contact.view.enter_name') ?>" required>
                </div>

                <!-- Email -->
                <div>
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-envelope mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('policy.contact.view.email') ?>
                    </label>
                    <input type="email" name="email" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('policy.contact.view.enter_email') ?>" required>
                </div>

                <!-- Subject -->
                <div>
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-tag mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('policy.contact.view.subject') ?>
                    </label>
                    <select name="subject" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" required>
                        <option value=""><?= $__('policy.contact.view.select_subject') ?></option>
                        <option value="general"><?= $__('policy.contact.view.general_inquiry') ?></option>
                        <option value="technical"><?= $__('policy.contact.view.technical_support') ?></option>
                        <option value="payment"><?= $__('policy.contact.view.payment_issue') ?></option>
                        <option value="account"><?= $__('policy.contact.view.account_issue') ?></option>
                        <option value="suggestion"><?= $__('policy.contact.view.suggestion') ?></option>
                        <option value="other"><?= $__('policy.contact.view.other') ?></option>
                    </select>
                </div>

                <!-- Message -->
                <div>
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-comment mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('policy.contact.view.message') ?>
                    </label>
                    <textarea name="message" rows="5" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('policy.contact.view.enter_message') ?>" required></textarea>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-4 rounded-lg font-gaming font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-lg">
                    <span class="btn-text"><?= $__('policy.contact.view.send_message') ?></span>
                </button>
            </form>
        </div>

        <!-- Contact Information -->
        <div class="space-y-6 sm:space-y-8">
            <!-- Contact Details -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200 p-4 sm:p-6 lg:p-8">
                <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('policy.contact.view.contact_info') ?></h2>

                <div class="space-y-4 sm:space-y-6">
                    <!-- Email -->
                    <div class="flex items-start">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                            <i class="fas fa-envelope text-blue-600 text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900"><?= $__('policy.contact.view.email_support') ?></h3>
                            <p class="text-gray-600 text-sm sm:text-base"><EMAIL></p>
                            <p class="text-xs sm:text-sm text-gray-500"><?= $__('policy.contact.view.email_response_time') ?></p>
                        </div>
                    </div>

                    <!-- Phone -->
                    <div class="flex items-start">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                            <i class="fas fa-phone text-green-600 text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900"><?= $__('policy.contact.view.phone_support') ?></h3>
                            <p class="text-gray-600 text-sm sm:text-base">1900-xxxx</p>
                            <p class="text-xs sm:text-sm text-gray-500"><?= $__('policy.contact.view.phone_hours') ?></p>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="flex items-start">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                            <i class="fas fa-map-marker-alt text-purple-600 text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-base sm:text-lg font-semibold text-gray-900"><?= $__('policy.contact.view.office_address') ?></h3>
                            <p class="text-gray-600 text-sm sm:text-base">Tp. Hồ Chí Minh, Việt Nam</p>
                            <p class="text-xs sm:text-sm text-gray-500"><?= $__('policy.contact.view.office_hours') ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ -->
            <div class="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200 p-4 sm:p-6 lg:p-8">
                <h2 class="text-xl sm:text-2xl font-semibold text-gray-900 mb-4 sm:mb-6"><?= $__('policy.contact.view.faq_title') ?></h2>

                <div class="space-y-3 sm:space-y-4">
                    <div class="border-b border-gray-200 pb-3 sm:pb-4">
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-1.5 sm:mb-2"><?= $__('policy.contact.view.faq_1_question') ?></h3>
                        <p class="text-gray-600 text-sm sm:text-base"><?= $__('policy.contact.view.faq_1_answer') ?></p>
                    </div>

                    <div class="border-b border-gray-200 pb-3 sm:pb-4">
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-1.5 sm:mb-2"><?= $__('policy.contact.view.faq_2_question') ?></h3>
                        <p class="text-gray-600 text-sm sm:text-base"><?= $__('policy.contact.view.faq_2_answer') ?></p>
                    </div>

                    <div>
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-1.5 sm:mb-2"><?= $__('policy.contact.view.faq_3_question') ?></h3>
                        <p class="text-gray-600 text-sm sm:text-base"><?= $__('policy.contact.view.faq_3_answer') ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Footer -->
    <div class="mt-8 sm:mt-12 lg:mt-16 bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-100 p-4 sm:p-6 lg:p-8">
        <!-- Mobile Layout -->
        <div class="block md:hidden space-y-3">
            <a href="<?= $this->url('/') ?>" class="w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-300 shadow-lg text-sm">
                <i class="fas fa-arrow-left mr-2"></i>
                <?= $__('policy.contact.view.back_home') ?>
            </a>
            <div class="grid grid-cols-2 gap-3">
                <a href="<?= $this->url('/terms') ?>" class="inline-flex items-center justify-center px-3 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg text-sm">
                    <i class="fas fa-file-contract mr-2"></i>
                    <span class="hidden sm:inline"><?= $__('policy.contact.view.terms_service') ?></span>
                    <span class="sm:hidden">Terms</span>
                </a>
                <a href="<?= $this->url('/privacy') ?>" class="inline-flex items-center justify-center px-3 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 shadow-lg text-sm">
                    <i class="fas fa-shield-alt mr-2"></i>
                    <span class="hidden sm:inline"><?= $__('policy.contact.view.privacy_policy') ?></span>
                    <span class="sm:hidden">Privacy</span>
                </a>
            </div>
        </div>

        <!-- Desktop Layout -->
        <div class="hidden md:flex justify-between items-center">
            <a href="<?= $this->url('/') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg">
                <i class="fas fa-arrow-left mr-3"></i>
                <?= $__('policy.contact.view.back_home') ?>
            </a>
            <div class="flex space-x-4">
                <a href="<?= $this->url('/terms') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-file-contract mr-3"></i>
                    <?= $__('policy.contact.view.terms_service') ?>
                </a>
                <a href="<?= $this->url('/privacy') ?>" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-lg hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-shield-alt mr-3"></i>
                    <?= $__('policy.contact.view.privacy_policy') ?>
                </a>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Contact form submission
    $('#contactForm').submit(function(e) {
        e.preventDefault();

        const formData = {
            name: $('input[name="name"]').val(),
            email: $('input[name="email"]').val(),
            subject: $('select[name="subject"]').val(),
            message: $('textarea[name="message"]').val(),
            csrf_token: $('input[name="csrf_token"]').val()
        };

        const submitBtn = $('button[type="submit"]');
        const originalText = submitBtn.find('.btn-text').text();

        submitBtn.prop('disabled', true).find('.btn-text').text('<?= $__('policy.contact.view.sending') ?>...');

        $.ajax({
            url: '<?= $this->url('/contact') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    // Clear form
                    $('#contactForm')[0].reset();
                    // Remove any error styling
                    $('#contactForm input, #contactForm select, #contactForm textarea').removeClass('border-red-500');
                } else {
                    GamingApp.showNotification(response.message, 'error');
                }
                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            },
            error: function(xhr) {
                let errorMessage = '<?= $__('policy.contact.view.error_occurred') ?>';

                try {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                    if (response && response.errors) {
                        // Display validation errors
                        Object.keys(response.errors).forEach(field => {
                            const input = $(`[name="${field}"]`);
                            input.addClass('border-red-500');
                            GamingApp.showNotification(response.errors[field][0], 'error');
                        });
                        submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                GamingApp.showNotification(errorMessage, 'error');
                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            }
        });
    });

    // Clear error styling on input focus
    $('input, select, textarea').focus(function() {
        $(this).removeClass('border-red-500');
    });
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(__DIR__)) . '/Views/layouts/app.php'; ?>
