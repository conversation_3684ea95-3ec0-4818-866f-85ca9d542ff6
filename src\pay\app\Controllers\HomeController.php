<?php

namespace Pay\Controllers;

use Core\Controller;

/**
 * Home Controller for Pay Domain
 * Handles home page and game selection
 */
class HomeController extends Controller
{
    public function __construct()
    {
        parent::__construct();

        // Override view to use Pay domain views
        $payViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($payViewPath);
    }

    /**
     * Show home page with game selection
     */
    public function index(): void
    {
        // Show game selection page for both authenticated and guest users
        $games = $this->getAvailableGames();

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('home');
        $structuredData = $this->seoHelper->generateStructuredData('website');

        $this->render('home/index', array_merge($this->getThemeData(), [
            'title' => 'Chọn Game',
            'games' => $games,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Get available games
     */
    private function getAvailableGames(): array
    {
        return [
            [
                'id' => 'lua_game',
                'name' => 'Nạp Lúa',
                'description' => 'Nạp lúa để quy đổi vào game bất kỳ',
                'image' => 'coin_big.png',
                'status' => 'online'
            ],
            [
                'id' => 'adventure_game',
                'name' => 'Adventure Quest',
                'description' => 'Game phiêu lưu và khám phá thế giới mới với đồ họa tuyệt đẹp',
                'image' => 'game01.png',
                'status' => 'online'
            ]
        ];
    }
}
