<?php

namespace Id\Controllers\Api;

use Core\Controller;
use Shared\Services\ApiService;

/**
 * Balance API Controller for Id Domain
 */
class BalanceController extends Controller
{
    private ApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();
    }

    /**
     * Refresh user balance
     */
    public function refresh(): void
    {
        // Clean output buffer to prevent extra characters
        if (ob_get_level()) {
            ob_clean();
        }

        header('Content-Type: application/json');

        try {
            // Check authentication
            if (!$this->isAuthenticated()) {
                http_response_code(401);
                echo json_encode([
                    'status' => false,
                    'message' => 'Unauthorized'
                ]);
                return;
            }

            $user = $this->getUser();

            // Get fresh balance from API using verify token (which returns user data)
            $response = $this->apiService->verifyToken($_SESSION['access_token']);
            $userData = $response["user"];

                // Update session with fresh data
            $_SESSION['user'] = array_merge($_SESSION['user'], [
                'balance' => $userData['balance'],
                'last_balance_update' => time()
            ]);

            // Format balance for display
            $balance = (float)($userData['balance'] ?? 0);

            echo json_encode([
                'status' => true,
                'message' => 'Balance updated successfully',
                'data' => [
                    'balance' => $userData['balance'],
                    'formatted_balance' => number_format($balance, 0, ',', '.')
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Balance refresh error: ' . $e->getMessage());

            // Return cached balance on error
            echo json_encode([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
