<?php

namespace Core;

use Core\Router;
use Core\View;

/**
 * Main Application Class
 * Handles application initialization and request processing
 */
class Application
{
    protected Router $router;
    protected View $view;
    protected array $config;
    private static ?Application $instance = null;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->router = new Router();
        $this->view = new View();

        // Set error reporting based on environment
        if ($this->config['app']['debug'] ?? false) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }

        // Start session
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        self::$instance = $this;
    }

    /**
     * Get application instance (Singleton pattern)
     */
    public static function getInstance(): ?Application
    {
        return self::$instance;
    }

    /**
     * Get configuration value
     */
    public function getConfig(string $key = null)
    {
        if ($key === null) {
            return $this->config;
        }

        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Get router instance
     */
    public function getRouter(): Router
    {
        return $this->router;
    }

    /**
     * Get view instance
     */
    public function getView(): View
    {
        return $this->view;
    }

    /**
     * Run the application
     */
    public function run(): void
    {
        try {
            // Set up routes
            $this->setupRoutes();

            // Handle the request
            $this->router->handleRequest();

        } catch (\Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Setup application routes
     */
    protected function setupRoutes(): void
    {
        // This method should be overridden by subclasses
        // For example, in IdApplication: $this->router->get('/', 'Id\Controllers\IndexController@index');
    }

    /**
     * Handle application exceptions
     */
    private function handleException(\Exception $e): void
    {
        if ($this->config['app']['debug'] ?? false) {
            echo "<h1>Application Error</h1>";
            echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        } else {
            // Log error and show user-friendly message
            error_log($e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

            http_response_code(500);
            $this->view->render('errors/500', [
                'title' => 'Lỗi hệ thống',
                'message' => 'Đã xảy ra lỗi. Vui lòng thử lại sau.'
            ]);
        }
    }

    /**
     * Get base URL
     */
    public function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['SCRIPT_NAME']);

        // Remove trailing slash if it's not root
        if ($path !== '/') {
            $path = rtrim($path, '/');
        }

        return $protocol . '://' . $host . $path;
    }

    /**
     * Redirect to URL
     */
    public function redirect(string $url, int $statusCode = 302): void
    {
        if (!str_starts_with($url, 'http')) {
            $baseUrl = rtrim($this->getBaseUrl(), '/');
            $url = $baseUrl . '/' . ltrim($url, '/');
        }

        header("Location: $url", true, $statusCode);
        exit;
    }
}
