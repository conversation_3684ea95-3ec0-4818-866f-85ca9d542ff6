# 🌐 MTF Game Gateway

Nginx Proxy Manager gateway để quản lý SSL và routing cho các domains của MTF Game.

## 🚀 Quick Start

### 1. Khởi động Gateway
```bash
cd gateway
chmod +x *.sh
./start-gateway.sh
```

### 2. Cấu hình Domains
- Truy cập: http://localhost:81
- Login: <EMAIL> / changeme
- Làm theo hướng dẫn trong `setup-domains.md`

### 3. Cập nhật Project hiện tại
Đổi port project hiện tại từ 80 sang 8080:

```yaml
# docker-compose.yml (project hiện tại)
services:
  nginx:
    ports:
      - "8080:80"  # Đổi từ "80:80" thành "8080:80"
```

Restart project:
```bash
docker-compose down
docker-compose up -d
```

## 📋 Cấu trúc thư mục

```
gateway/
├── docker-compose.yml      # Gateway configuration
├── start-gateway.sh        # Script khởi động
├── stop-gateway.sh         # Script dừng
├── setup-domains.md        # Hướng dẫn cấu hình domains
├── data/                   # Nginx Proxy Manager data (auto-created)
└── letsencrypt/           # SSL certificates (auto-created)
```

## 🔧 Ports sử dụng

- **80**: HTTP Gateway (Nginx Proxy Manager)
- **443**: HTTPS Gateway (Nginx Proxy Manager)  
- **81**: Admin Web Interface
- **8080**: Project hiện tại (sau khi đổi port)

## 🌐 Domains được quản lý

- `pay.mtfgame.com` → `localhost:8080`
- `id.mtfgame.com` → `localhost:8080`

## 🔒 SSL Certificates

- Tự động tạo SSL certificates qua Let's Encrypt
- Tự động renewal
- Force HTTPS redirect
- HSTS enabled

## 🛠️ Commands

```bash
# Khởi động gateway
./start-gateway.sh

# Dừng gateway
./stop-gateway.sh

# Xem logs
docker-compose logs -f

# Restart gateway
docker-compose restart

# Update gateway
docker-compose pull
docker-compose up -d
```

## 🔍 Troubleshooting

### Gateway không start được:
```bash
# Kiểm tra port 80 có bị chiếm không
sudo netstat -tulpn | grep :80

# Kiểm tra logs
docker-compose logs nginx-proxy-manager
```

### Không connect được tới project:
1. Kiểm tra project đã chạy trên port 8080
2. Test direct access: http://localhost:8080
3. Kiểm tra network connectivity

### SSL không hoạt động:
1. Kiểm tra domains đã point đúng IP
2. Kiểm tra firewall cho port 80, 443
3. Kiểm tra email hợp lệ trong Let's Encrypt

## 📞 Support

Nếu có vấn đề, kiểm tra:
1. `docker-compose logs`
2. Admin interface tại http://localhost:81
3. Project logs tại http://localhost:8080
