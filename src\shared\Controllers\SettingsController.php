<?php

namespace Shared\Controllers;

use Core\Controller;

/**
 * Settings Controller
 * Handles language switching
 */
class SettingsController extends Controller
{

    /**
     * Switch language
     */
    public function switchLanguage(): void
    {
        header('Content-Type: application/json');

        try {
            $language = $this->input('language');

            if (!$language) {
                throw new \Exception('Language parameter is required');
            }

            // Get available languages from config
            $availableLanguages = array_keys($this->app->getConfig()['ui']['languages'] ?? []);

            if (in_array($language, $availableLanguages)) {
                // Set language in session
                $_SESSION['language'] = $language;

                // Update language helper instance
                $this->languageHelper->setLanguage($language);

                $this->json([
                    'status' => true,
                    'message' => 'Language switched successfully',
                    'language' => $language,
                    'reload' => true // Signal frontend to reload page
                ]);
            } else {
                throw new \Exception('Invalid language');
            }

        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get current settings
     */
    public function getSettings(): void
    {
        header('Content-Type: application/json');

        try {
            $this->json([
                'status' => true,
                'data' => [
                    'language' => $this->languageHelper->getCurrentLanguage(),
                    'availableLanguages' => $this->app->getConfig()['ui']['languages'] ?? []
                ]
            ]);

        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
