<?php

namespace Id\Controllers;

use Core\Controller;

/**
 * History Controller for Id Domain
 * Handles transaction history functionality
 */
class HistoryController extends Controller
{
    public function __construct()
    {
        parent::__construct();

        // Override view to use ID domain views
        $idViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($idViewPath);
    }

    /**
     * Show transaction history page
     */
    public function index(): void
    {
        $user = $this->getUser();

        // Get filter parameters
        $filters = [
            'type' => $this->input('type', 'all'),
            'status' => $this->input('status', 'all'),
            'date_from' => $this->input('date_from'),
            'date_to' => $this->input('date_to'),
            'page' => (int) $this->input('page', 1)
        ];

        // Get transactions
        $transactions = $this->getTransactions($user, $filters);

        // Get summary statistics
        $summary = $this->getTransactionSummary($user);

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('history');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('history/index', array_merge($this->getThemeData(), [
            'title' => 'Lịch sử giao dịch',
            'user' => $user,
            'transactions' => $transactions,
            'summary' => $summary,
            'filters' => $filters,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Get transactions for user (mock implementation)
     */
    private function getTransactions(array $user, array $filters): array
    {
        // Mock transaction data
        $allTransactions = [
            [
                'id' => 'TXN001',
                'type' => 'deposit',
                'amount' => 100000,
                'status' => 'completed',
                'description' => 'Nạp tiền qua MoMo',
                'created_at' => '2024-05-20 10:30:00',
                'payment_method' => 'momo'
            ],
            [
                'id' => 'TXN002',
                'type' => 'deposit',
                'amount' => 50000,
                'status' => 'pending',
                'description' => 'Nạp tiền qua chuyển khoản',
                'created_at' => '2024-05-19 15:45:00',
                'payment_method' => 'bank'
            ],
            [
                'id' => 'TXN003',
                'type' => 'withdraw',
                'amount' => 25000,
                'status' => 'completed',
                'description' => 'Rút tiền về tài khoản',
                'created_at' => '2024-05-18 09:15:00',
                'payment_method' => 'bank'
            ],
            [
                'id' => 'TXN004',
                'type' => 'game',
                'amount' => 10000,
                'status' => 'completed',
                'description' => 'Mua vật phẩm trong game',
                'created_at' => '2024-05-17 20:30:00',
                'payment_method' => 'balance'
            ],
            [
                'id' => 'TXN005',
                'type' => 'deposit',
                'amount' => 200000,
                'status' => 'completed',
                'description' => 'Nạp tiền qua MoMo',
                'created_at' => '2024-05-16 14:20:00',
                'payment_method' => 'momo'
            ]
        ];

        // Apply filters
        $filteredTransactions = array_filter($allTransactions, function($transaction) use ($filters) {
            // Filter by type
            if ($filters['type'] !== 'all' && $transaction['type'] !== $filters['type']) {
                return false;
            }

            // Filter by status
            if ($filters['status'] !== 'all' && $transaction['status'] !== $filters['status']) {
                return false;
            }

            // Filter by date range
            if ($filters['date_from'] && $transaction['created_at'] < $filters['date_from'] . ' 00:00:00') {
                return false;
            }

            if ($filters['date_to'] && $transaction['created_at'] > $filters['date_to'] . ' 23:59:59') {
                return false;
            }

            return true;
        });

        // Sort by date (newest first)
        usort($filteredTransactions, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        // Pagination
        $perPage = 10;
        $offset = ($filters['page'] - 1) * $perPage;
        $paginatedTransactions = array_slice($filteredTransactions, $offset, $perPage);

        return [
            'data' => $paginatedTransactions,
            'total' => count($filteredTransactions),
            'per_page' => $perPage,
            'current_page' => $filters['page'],
            'total_pages' => ceil(count($filteredTransactions) / $perPage)
        ];
    }

    /**
     * Get transaction summary statistics
     */
    private function getTransactionSummary(array $user): array
    {
        // Mock summary data
        return [
            'total_deposits' => 350000,
            'total_withdrawals' => 25000,
            'total_spent' => 10000,
            'current_balance' => $user['balance'] ?? 0,
            'this_month_deposits' => 150000,
            'this_month_spent' => 10000,
            'transaction_count' => [
                'total' => 5,
                'pending' => 1,
                'completed' => 4,
                'failed' => 0
            ]
        ];
    }
}
