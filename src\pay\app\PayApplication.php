<?php

namespace Pay\App;

use Core\Application;
use Core\Router;

/**
 * Pay Domain Application
 * Handles pay.mtfgame.com - Home & Deposit functionality
 */
class PayApplication extends Application
{
    public function __construct(array $config = [])
    {
        parent::__construct($config);

        // Set view path for pay domain
        $payViewPath = __DIR__ . '/Views/';
        $this->view = new \Core\View($payViewPath);
    }

    /**
     * Setup routes for pay domain
     */
    protected function setupRoutes(): void
    {
        // Get router from parent
        $router = $this->getRouter();

        // Public routes
        $router->get('/', 'Pay\Controllers\HomeController@index');

        // Game guide routes (handle authentication internally)
        $router->get('/game/{gameId}/guide', 'Pay\Controllers\GameGuideController@guide');

        // Deposit routes (handle authentication internally)
        $router->get('/deposit', 'Pay\Controllers\DepositController@index');
        $router->post('/deposit', 'Pay\Controllers\DepositController@process');

        // Ngân Lượng callback routes (public access)
        $router->get('/deposit/success', 'Pay\Controllers\DepositController@success');
        $router->get('/deposit/cancel', 'Pay\Controllers\DepositController@cancel');
        $router->post('/deposit/notify', 'Pay\Controllers\DepositController@notify');
        $router->get('/deposit/notify', 'Pay\Controllers\DepositController@notify'); // Some gateways use GET

        // AJAX routes for deposit
        $router->post('/deposit/check-status', 'Pay\Controllers\DepositController@checkStatus');

        // Policy routes (public access) - using shared controller
        $router->get('/terms', 'Shared\Controllers\PolicyController@terms');
        $router->get('/privacy', 'Shared\Controllers\PolicyController@privacy');
        $router->get('/contact', 'Shared\Controllers\PolicyController@contact');
        $router->post('/contact', 'Shared\Controllers\PolicyController@submitContact');

        // API routes
        $router->group(['prefix' => 'api'], function($router) {
            // Settings routes
            $router->post('/settings/language', 'Shared\Controllers\SettingsController@switchLanguage');
            $router->get('/settings', 'Shared\Controllers\SettingsController@getSettings');
        });

        // SEO routes
        $router->get('/sitemap.xml', 'Shared\Controllers\SitemapController@index');
    }

    /**
     * Check if user is authenticated, redirect to id domain if not
     */
    public function checkAuthentication(): bool
    {
        if (!isset($_SESSION['user']) || !isset($_SESSION['access_token'])) {
            // Get current URL for return link
            $currentUrl = $this->getCurrentUrl();
            $returnUrl = urlencode($currentUrl);

            // Load DomainHelper if not loaded
            if (!class_exists('Shared\Helpers\DomainHelper')) {
                require_once dirname(dirname(__DIR__)) . '/shared/Helpers/DomainHelper.php';
            }

            // Redirect to id domain for authentication
            $domainHelper = \Shared\Helpers\DomainHelper::getInstance();
            $loginUrl = $domainHelper->getLoginUrl($currentUrl);
            header("Location: " . $loginUrl);
            exit;
        }

        return true;
    }

    /**
     * Get current URL for return link
     */
    private function getCurrentUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'];

        return $protocol . '://' . $host . $uri;
    }

    /**
     * Run the pay application
     */
    public function run(): void
    {
        // Use parent's run method which handles everything correctly
        parent::run();
    }
}
