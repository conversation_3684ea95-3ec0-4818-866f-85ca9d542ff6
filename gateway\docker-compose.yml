version: '3.8'

services:
  nginx-proxy-manager:
    image: 'jc21/nginx-proxy-manager:latest'
    container_name: nginx-proxy-manager
    restart: unless-stopped
    ports:
      # HTTP Gateway - chiếm port 80 chính
      - '80:80'
      # HTTPS Gateway - sử dụng port 8443 thay vì 443
      - '443:443'
      # Admin Web Interface
      - '82:81'
    volumes:
      # Persistent data
      - ./data:/data
      # SSL certificates
      - ./letsencrypt:/etc/letsencrypt
    environment:
      # Disable IPv6 if not needed
      DISABLE_IPV6: 'true'
    networks:
      - proxy-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:81"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  proxy-network:
    name: mtfgame-proxy
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
