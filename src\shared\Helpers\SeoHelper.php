<?php

namespace Shared\Helpers;

/**
 * SEO Helper Class
 * Manages SEO meta tags, structured data, and optimization
 */
class SeoH<PERSON>per
{
    private static ?SeoHelper $instance = null;
    private string $appName;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct(string $appName = 'MtfGame')
    {
        $this->appName = $appName;
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(string $appName = 'MtfGame'): SeoHelper
    {
        if (self::$instance === null) {
            self::$instance = new self($appName);
        }
        return self::$instance;
    }

    /**
     * Generate SEO data for pages
     */
    public function generateSeoData(string $page, array $data = []): array
    {
        $domainHelper = DomainHelper::getInstance();
        $baseUrl = $domainHelper->getCurrentDomainUrl();
        $currentUrl = $baseUrl . ($_SERVER['REQUEST_URI'] ?? '/');

        // Default SEO data
        $seoData = [
            'title' => $this->appName . ' - Gaming Account Manager',
            'description' => $this->appName . ' - <PERSON><PERSON> thống quản lý tài khoản game chuyên nghiệp với nạp tiền nhanh chóng và bảo mật cao.',
            'keywords' => 'game, nạp tiền, gaming, tài khoản game, ' . strtolower($this->appName),
            'robots' => 'index, follow',
            'canonical' => $currentUrl,
            'og_type' => 'website',
            'og_url' => $currentUrl,
            'og_image' => $baseUrl . '/assets/images/logo_mtf.png'
        ];

        // Page-specific SEO data
        switch ($page) {
            case 'home':
                $seoData = array_merge($seoData, [
                    'title' => $this->appName . ' - Trang chủ | Chọn game và nạp tiền',
                    'description' => 'Chọn game yêu thích và nạp tiền nhanh chóng tại ' . $this->appName . '. Hệ thống bảo mật cao, giao dịch an toàn.',
                    'keywords' => strtolower($this->appName) . ', game online, nạp tiền game, chọn game, gaming platform',
                    'og_title' => $this->appName . ' - Trang chủ',
                    'og_description' => 'Chọn game yêu thích và nạp tiền nhanh chóng tại ' . $this->appName
                ]);
                break;

            case 'login':
                $seoData = array_merge($seoData, [
                    'title' => 'Đăng nhập - ' . $this->appName,
                    'description' => 'Đăng nhập vào tài khoản ' . $this->appName . ' để quản lý game và nạp tiền.',
                    'keywords' => 'đăng nhập, login, ' . strtolower($this->appName) . ', tài khoản game',
                    'robots' => 'noindex, nofollow',
                    'og_title' => 'Đăng nhập - ' . $this->appName,
                    'og_description' => 'Đăng nhập vào tài khoản ' . $this->appName
                ]);
                break;

            case 'register':
                $seoData = array_merge($seoData, [
                    'title' => 'Đăng ký tài khoản - ' . $this->appName,
                    'description' => 'Tạo tài khoản ' . $this->appName . ' miễn phí để bắt đầu chơi game và nạp tiền.',
                    'keywords' => 'đăng ký, register, tạo tài khoản, ' . strtolower($this->appName),
                    'og_title' => 'Đăng ký tài khoản - ' . $this->appName,
                    'og_description' => 'Tạo tài khoản ' . $this->appName . ' miễn phí'
                ]);
                break;

            case 'profile':
                $seoData = array_merge($seoData, [
                    'title' => 'Thông tin tài khoản - ' . $this->appName,
                    'description' => 'Quản lý thông tin cá nhân, xác thực tài khoản và bảo mật tại ' . $this->appName . '.',
                    'keywords' => 'profile, thông tin tài khoản, xác thực, bảo mật',
                    'robots' => 'noindex, nofollow',
                    'og_title' => 'Thông tin tài khoản - ' . $this->appName,
                    'og_description' => 'Quản lý thông tin cá nhân tại ' . $this->appName
                ]);
                break;

            case 'deposit':
                $seoData = array_merge($seoData, [
                    'title' => 'Nạp tiền - ' . $this->appName,
                    'description' => 'Nạp tiền vào game nhanh chóng và an toàn. Nhiều gói nạp với bonus hấp dẫn.',
                    'keywords' => 'nạp tiền, deposit, gaming, bonus, gói nạp',
                    'og_title' => 'Nạp tiền - ' . $this->appName,
                    'og_description' => 'Nạp tiền vào game nhanh chóng và an toàn'
                ]);
                break;

            case 'history':
                $seoData = array_merge($seoData, [
                    'title' => 'Lịch sử giao dịch - ' . $this->appName,
                    'description' => 'Xem lịch sử giao dịch nạp tiền và quản lý tài chính game của bạn.',
                    'keywords' => 'lịch sử, giao dịch, transaction history, nạp tiền',
                    'robots' => 'noindex, nofollow',
                    'og_title' => 'Lịch sử giao dịch - ' . $this->appName,
                    'og_description' => 'Xem lịch sử giao dịch tại ' . $this->appName
                ]);
                break;

            case 'forgot-password':
                $seoData = array_merge($seoData, [
                    'title' => 'Quên mật khẩu - ' . $this->appName,
                    'description' => 'Khôi phục mật khẩu tài khoản ' . $this->appName . ' một cách nhanh chóng và bảo mật.',
                    'keywords' => 'quên mật khẩu, forgot password, khôi phục tài khoản',
                    'robots' => 'noindex, nofollow',
                    'og_title' => 'Quên mật khẩu - ' . $this->appName,
                    'og_description' => 'Khôi phục mật khẩu tài khoản ' . $this->appName
                ]);
                break;

            case 'game_guide':
                $gameName = $data['game_name'] ?? 'Game';
                $seoData = array_merge($seoData, [
                    'title' => 'Hướng dẫn nạp ' . $gameName . ' - ' . $this->appName,
                    'description' => 'Hướng dẫn chi tiết cách nạp Lúa và sử dụng trong game ' . $gameName . '. Quy trình đơn giản, nhanh chóng và an toàn.',
                    'keywords' => 'hướng dẫn nạp, ' . strtolower($gameName) . ', nạp lúa, game guide, tutorial, gaming',
                    'og_title' => 'Hướng dẫn nạp ' . $gameName . ' - ' . $this->appName,
                    'og_description' => 'Hướng dẫn chi tiết cách nạp Lúa và sử dụng trong game ' . $gameName,
                    'og_type' => 'article'
                ]);
                break;
        }

        // Merge with custom data
        if (!empty($data)) {
            $seoData = array_merge($seoData, $data);
        }

        return $seoData;
    }

    /**
     * Generate JSON-LD structured data
     */
    public function generateStructuredData(string $type, array $data = []): string
    {
        $domainHelper = DomainHelper::getInstance();
        $baseUrl = $domainHelper->getCurrentDomainUrl();

        switch ($type) {
            case 'website':
                $structuredData = [
                    '@context' => 'https://schema.org',
                    '@type' => 'WebSite',
                    'name' => $this->appName,
                    'url' => $baseUrl,
                    'description' => 'Hệ thống quản lý tài khoản game chuyên nghiệp',
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => $baseUrl . '/search?q={search_term_string}',
                        'query-input' => 'required name=search_term_string'
                    ]
                ];
                break;

            case 'organization':
                $structuredData = [
                    '@context' => 'https://schema.org',
                    '@type' => 'Organization',
                    'name' => $this->appName,
                    'url' => $baseUrl,
                    'logo' => $baseUrl . '/assets/images/logo_mtf.png',
                    'description' => 'Hệ thống quản lý tài khoản game chuyên nghiệp',
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'contactType' => 'customer service',
                        'availableLanguage' => 'Vietnamese'
                    ]
                ];
                break;

            case 'breadcrumb':
                $structuredData = [
                    '@context' => 'https://schema.org',
                    '@type' => 'BreadcrumbList',
                    'itemListElement' => $data['items'] ?? []
                ];
                break;

            case 'game_guide':
                $structuredData = [
                    '@context' => 'https://schema.org',
                    '@type' => 'HowTo',
                    'name' => 'Hướng dẫn nạp ' . ($data['game_name'] ?? 'Game'),
                    'description' => 'Hướng dẫn chi tiết cách nạp Lúa và sử dụng trong game',
                    'image' => $baseUrl . '/assets/images/game-deposit-guide.png',
                    'totalTime' => 'PT5M',
                    'estimatedCost' => [
                        '@type' => 'MonetaryAmount',
                        'currency' => 'VND',
                        'value' => '20000'
                    ],
                    'supply' => [
                        [
                            '@type' => 'HowToSupply',
                            'name' => 'Tài khoản game'
                        ],
                        [
                            '@type' => 'HowToSupply',
                            'name' => 'Lúa trong hệ thống'
                        ]
                    ],
                    'step' => [
                        [
                            '@type' => 'HowToStep',
                            'name' => 'Nạp Lúa vào tài khoản',
                            'text' => 'Truy cập trang nạp tiền và chọn gói Lúa phù hợp',
                            'url' => $baseUrl . '/deposit'
                        ],
                        [
                            '@type' => 'HowToStep',
                            'name' => 'Sử dụng Lúa trong game',
                            'text' => 'Vào game, chọn gói cần nạp và thanh toán bằng Lúa',
                            'image' => $baseUrl . '/assets/images/game-deposit-guide.png'
                        ]
                    ]
                ];
                break;

            default:
                $structuredData = [];
        }

        // Merge with custom data
        if (!empty($data) && $type !== 'breadcrumb') {
            $structuredData = array_merge($structuredData, $data);
        }

        return json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /**
     * Generate breadcrumb items for structured data
     */
    public static function generateBreadcrumbs(array $items): array
    {
        $breadcrumbs = [];
        $position = 1;

        foreach ($items as $item) {
            $breadcrumbs[] = [
                '@type' => 'ListItem',
                'position' => $position,
                'name' => $item['name'],
                'item' => $item['url']
            ];
            $position++;
        }

        return $breadcrumbs;
    }

    /**
     * Get current domain URL
     */
    private static function getCurrentDomainUrl(): string
    {
        return DomainHelper::getCurrentDomainUrl();
    }
}
