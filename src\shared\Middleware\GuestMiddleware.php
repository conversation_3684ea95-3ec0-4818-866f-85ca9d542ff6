<?php

namespace Shared\Middleware;

use Core\Application;

/**
 * Guest Middleware
 * Redirects authenticated users away from guest-only pages (like login)
 */
class GuestMiddleware
{
    /**
     * Handle the middleware
     */
    public function handle(): bool
    {
        // If user is already authenticated, redirect to dashboard
        if (isset($_SESSION['user']) && isset($_SESSION['access_token'])) {
            $app = Application::getInstance();
            $app->redirect('/');
            return false;
        }

        return true;
    }
}
