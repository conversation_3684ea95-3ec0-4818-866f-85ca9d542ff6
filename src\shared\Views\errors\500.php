<?php $this->startSection('content'); ?>

<div class="error-container">
    <div class="error-content">
        <div class="error-animation">
            <div class="error-code">500</div>
            <div class="error-sparks">
                <div class="spark"></div>
                <div class="spark"></div>
                <div class="spark"></div>
                <div class="spark"></div>
            </div>
        </div>

        <h1 class="error-title"><?= $__('shared.errors.server_error') ?></h1>
        <p class="error-message">
            <?= $this->escape($message ?? $__('shared.errors.server_error_message')) ?>
        </p>

        <div class="error-actions">
            <a href="<?= $this->url('/') ?>" class="btn gaming-btn gaming-btn-primary">
                <i class="fas fa-home"></i>
                <?= $__('shared.header.home') ?>
            </a>
            <button onclick="location.reload()" class="btn gaming-btn gaming-btn-secondary">
                <i class="fas fa-redo"></i>
                <?= $__('shared.errors.try_again') ?>
            </button>
        </div>
    </div>
</div>

<style>
.error-sparks {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
}

.spark {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: spark 2s ease-in-out infinite;
    box-shadow: 0 0 10px var(--primary-color);
}

.spark:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.spark:nth-child(2) {
    top: 20%;
    right: 20%;
    animation-delay: 0.5s;
}

.spark:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 1s;
}

.spark:nth-child(4) {
    bottom: 20%;
    right: 20%;
    animation-delay: 1.5s;
}

@keyframes spark {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>

<?php $this->endSection(); ?>

<!-- Include the layout -->
<?php include __DIR__ . '/../layouts/app.php'; ?>
