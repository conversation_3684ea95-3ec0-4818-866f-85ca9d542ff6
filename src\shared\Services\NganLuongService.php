<?php

namespace Shared\Services;

/**
 * Ngân Lượng Payment Service
 * Handles integration with Ngân Lượng payment gateway
 */
class NganLuongService
{
    private string $merchantId;
    private string $merchantPassword;
    private string $receiverEmail;
    private string $apiUrl;
    private string $checkUrl;
    private bool $isTestMode;

    public function __construct()
    {

        $config = require __DIR__ . '/../config/nganluong.php';

        $this->isTestMode = $config['test_mode'] ?? true;

        if ($this->isTestMode) {
            $this->merchantId = $config['test']['merchant_id'];
            $this->merchantPassword = $config['test']['merchant_password'];
            $this->receiverEmail = $config['test']['receiver_email'];
            $this->apiUrl = $config['test']['api_url'];
            $this->checkUrl = $config['test']['check_url'];
        } else {
            $this->merchantId = $config['live']['merchant_id'];
            $this->merchantPassword = $config['live']['merchant_password'];
            $this->receiverEmail = $config['live']['receiver_email'];
            $this->apiUrl = $config['live']['api_url'];
            $this->checkUrl = $config['live']['check_url'];
        }
    }

    /**
     * Create payment order
     */
    public function createOrder(array $orderData): array
    {
        $params = [
            'merchant_id' => $this->merchantId,
            'merchant_password' => md5($this->merchantPassword),
            'version' => '3.1',
            'function' => 'SetExpressCheckout',
            'receiver_email' => $this->receiverEmail,
            'order_code' => $orderData['order_code'],
            'total_amount' => $orderData['total_amount'],
            'payment_method' => $orderData['payment_method'],
            'bank_code' => $orderData['bank_code'] ?? '',
            'order_description' => $orderData['order_description'] ?? '',
            'return_url' => $orderData['return_url'],
            'notify_url' => $orderData['notify_url'],
            'cancel_url' => $orderData['cancel_url'],
            'time_limit' => $orderData['time_limit'] ?? 1440, // 24 hours
            'buyer_fullname' => $orderData['buyer_fullname'],
            'buyer_email' => $orderData['buyer_email'],
            'buyer_mobile' => $orderData['buyer_mobile'],
            'buyer_address' => $orderData['buyer_address'] ?? '',
            'cur_code' => 'vnd',
            'lang_code' => 'vi'
        ];

        $response = $this->makeRequest($this->apiUrl, $params);

        if ($response === false) {
            throw new \Exception('Không thể kết nối đến Ngân Lượng');
        }

        return $this->parseXmlResponse($response);
    }

    /**
     * Check order status
     */
    public function checkOrderStatus(string $token): array
    {
        $checksum = md5($token . '|' . $this->merchantPassword);

        $params = [
            'merchant_id' => $this->merchantId,
            'token' => $token,
            'checksum' => $checksum
        ];

        $response = $this->makeRequest($this->checkUrl, $params);

        if ($response === false) {
            throw new \Exception('Không thể kiểm tra trạng thái đơn hàng');
        }

        return json_decode($response, true);
    }

    /**
     * Get available payment methods categorized
     */
    public function getPaymentMethods(): array
    {
        return [
            'banks' => [
                'title' => 'Ngân hàng',
                'methods' => [
                    'ATM_ONLINE' => [
                        'name' => 'Thẻ ATM nội địa',
                        'description' => 'Thanh toán bằng thẻ ATM các ngân hàng Việt Nam',
                        'icon' => 'fas fa-credit-card',
                        'min_amount' => 2000,
                        'banks' => $this->getATMBanks()
                    ],
                    'IB_ONLINE' => [
                        'name' => 'Internet Banking',
                        'description' => 'Thanh toán qua Internet Banking',
                        'icon' => 'fas fa-university',
                        'min_amount' => 2000,
                        'banks' => $this->getIBSupportedBanks()
                    ],
                    'QRCODE' => [
                        'name' => 'QR Code',
                        'description' => 'Thanh toán bằng mã QR',
                        'icon' => 'fas fa-qrcode',
                        'min_amount' => 2000,
                        'banks' => $this->getQRCodeSupportedBanks()
                    ],
                    'QRCODE247' => [
                        'name' => 'QR Code VietQR',
                        'description' => 'Thanh toán bằng mã QR VietQR',
                        'icon' => 'fas fa-qrcode',
                        'min_amount' => 50000,
                        'banks' => $this->getQR247Banks()
                    ],
                    'BANK_TRANSFER_ONLINE' => [
                        'name' => 'Chuyển khoản ngân hàng',
                        'description' => 'Chuyển khoản trực tuyến qua ngân hàng',
                        'icon' => 'fas fa-exchange-alt',
                        'min_amount' => 50000,
                        'banks' => $this->getBankTransferBanks()
                    ],
                    'VISA' => [
                        'name' => 'Thẻ Visa/MasterCard/JCB',
                        'description' => 'Thanh toán bằng thẻ quốc tế',
                        'icon' => 'fab fa-cc-visa',
                        'min_amount' => 2000,
                        'banks' => ['VISA', 'MASTER', 'JCB']
                    ]
                ]
            ],
            'wallets' => [
                'title' => 'Ví điện tử',
                'methods' => [
                    'NL' => [
                        'name' => 'Ví Ngân Lượng',
                        'description' => 'Thanh toán bằng số dư ví Ngân Lượng',
                        'icon' => 'fas fa-wallet',
                        'min_amount' => 2000,
                        'banks' => []
                    ],
                    'ZALOPAY' => [
                        'name' => 'ZaloPay',
                        'description' => 'Thanh toán qua ví ZaloPay (QR Code)',
                        'icon' => 'fas fa-mobile-alt',
                        'min_amount' => 50000, // Sử dụng QRCODE247
                        'banks' => []
                    ],
                    'MOMO' => [
                        'name' => 'MoMo',
                        'description' => 'Thanh toán qua ví MoMo (QR Code)',
                        'icon' => 'fas fa-mobile-alt',
                        'min_amount' => 50000, // Sử dụng QRCODE247
                        'banks' => []
                    ]
                ]
            ]
        ];
    }

    /**
     * Get supported ATM banks
     */
    private function getATMBanks(): array
    {
        return [
            'VCB' => 'Vietcombank',
            'TCB' => 'Techcombank',
            'BIDV' => 'BIDV',
            'VTB' => 'VietinBank',
            'ACB' => 'ACB',
            'MB' => 'MB Bank',
            'VPB' => 'VPBank',
            'STB' => 'Sacombank',
            'EXB' => 'Eximbank',
            'HDB' => 'HDBank',
            'MSB' => 'MSB',
            'NVB' => 'NVB',
            'VAB' => 'VietA Bank',
            'BAB' => 'Bac A Bank',
            'AGB' => 'Agribank',
            'OJB' => 'OceanBank',
            'PGB' => 'PGBank',
            'SHB' => 'SHB',
            'TPB' => 'TPBank',
            'NAB' => 'Nam A Bank',
            'SGB' => 'Saigon Bank',
            'ABB' => 'ABBANK',
            'SCB' => 'SCB',
            'IVB' => 'IVB',
            'OCB' => 'OCB',
            'SEA' => 'SeaBank',
            'LVB' => 'LienVietPostBank',
            'WRB' => 'Woori Bank',
            'PVCOMBANK' => 'PVcomBank',
            'VCCB' => 'Ban Viet Bank',
            'BVB' => 'Bao Viet Bank',
            'SHNB' => 'Shinhan Bank',
            'KLB' => 'Kien Long Bank'
        ];
    }

    /**
     * Get Internet Banking supported banks (based on official support table)
     */
    private function getIBSupportedBanks(): array
    {
        return [
            'VCB' => 'Vietcombank',
            'DAB' => 'DongA Bank',
            'TCB' => 'Techcombank',
            'MB' => 'MB Bank',
            'VIB' => 'VIB',
            'AGB' => 'Agribank',
            'BIDV' => 'BIDV',
            'ABB' => 'ABBANK',
            'OCB' => 'OCB',
            'PVCOMBANK' => 'PVcomBank',
            'VCCB' => 'Ban Viet Bank',
            'SHNB' => 'Shinhan Bank'
        ];
    }

    /**
     * Get QR Code supported banks (based on official support table)
     */
    private function getQRCodeSupportedBanks(): array
    {
        return [
            'MB' => 'MB Bank',
            'VIB' => 'VIB',
            'ICB' => 'VietinBank',
            'EXB' => 'Eximbank',
            'ACB' => 'ACB',
            'HDB' => 'HDBank',
            'MSB' => 'MSB',
            'NVB' => 'NVB',
            'VAB' => 'VietA Bank',
            'VPB' => 'VPBank',
            'STB' => 'Sacombank',
            'BAB' => 'Bac A Bank',
            'AGB' => 'Agribank',
            'BIDV' => 'BIDV',
            'OJB' => 'OceanBank',
            'SHB' => 'SHB',
            'TPB' => 'TPBank',
            'NAB' => 'Nam A Bank',
            'SGB' => 'Saigon Bank',
            'ABB' => 'ABBANK',
            'SCB' => 'SCB',
            'IVB' => 'IVB',
            'OCB' => 'OCB',
            'SEA' => 'SeaBank',
            'WRB' => 'Woori Bank',
            'PVCOMBANK' => 'PVcomBank',
            'BVB' => 'Bao Viet Bank',
            'SHNB' => 'Shinhan Bank',
            'KLB' => 'Kien Long Bank'
        ];
    }

    /**
     * Get QRCODE247 supported banks (based on official support table)
     */
    private function getQR247Banks(): array
    {
        return [
            'VCB' => 'Vietcombank',
            'TCB' => 'Techcombank',
            'MB' => 'MB Bank',
            'VIB' => 'VIB',
            'ICB' => 'VietinBank',
            'EXB' => 'Eximbank',
            'ACB' => 'ACB',
            'HDB' => 'HDBank',
            'MSB' => 'MSB',
            'NVB' => 'NVB',
            'VAB' => 'VietA Bank',
            'VPB' => 'VPBank',
            'STB' => 'Sacombank',
            'BAB' => 'Bac A Bank',
            'AGB' => 'Agribank',
            'BIDV' => 'BIDV',
            'OJB' => 'OceanBank',
            'PGB' => 'PGBank',
            'SHB' => 'SHB',
            'TPB' => 'TPBank',
            'NAB' => 'Nam A Bank',
            'SGB' => 'Saigon Bank',
            'ABB' => 'ABBANK',
            'SCB' => 'SCB',
            'IVB' => 'IVB',
            'OCB' => 'OCB',
            'SEA' => 'SeaBank',
            'LVB' => 'LienVietPostBank',
            'WRB' => 'Woori Bank',
            'PVCOMBANK' => 'PVcomBank',
            'VCCB' => 'Ban Viet Bank',
            'BVB' => 'Bao Viet Bank',
            'SHNB' => 'Shinhan Bank',
            'KLB' => 'Kien Long Bank'
        ];
    }

    /**
     * Get BANK_TRANSFER_ONLINE supported banks (based on official support table)
     */
    private function getBankTransferBanks(): array
    {
        return [
            'VCB' => 'Vietcombank',
            'DAB' => 'DongA Bank',
            'TCB' => 'Techcombank',
            'MB' => 'MB Bank',
            'VIB' => 'VIB',
            'ICB' => 'VietinBank',
            'EXB' => 'Eximbank',
            'ACB' => 'ACB',
            'HDB' => 'HDBank',
            'MSB' => 'MSB',
            'NVB' => 'NVB',
            'VAB' => 'VietA Bank',
            'VPB' => 'VPBank',
            'STB' => 'Sacombank',
            'BAB' => 'Bac A Bank',
            'AGB' => 'Agribank',
            'BIDV' => 'BIDV',
            'OJB' => 'OceanBank',
            'PGB' => 'PGBank',
            'SHB' => 'SHB',
            'TPB' => 'TPBank',
            'NAB' => 'Nam A Bank',
            'SGB' => 'Saigon Bank',
            'ABB' => 'ABBANK',
            'SCB' => 'SCB',
            'IVB' => 'IVB',
            'OCB' => 'OCB',
            'SEA' => 'SeaBank',
            'LVB' => 'LienVietPostBank',
            'WRB' => 'Woori Bank',
            'PVCOMBANK' => 'PVcomBank',
            'VCCB' => 'Ban Viet Bank',
            'BVB' => 'Bao Viet Bank',
            'SHNB' => 'Shinhan Bank',
            'KLB' => 'Kien Long Bank'
        ];
    }

    /**
     * Make HTTP request to Ngân Lượng API
     */
    private function makeRequest(string $url, array $params)
    {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/x-www-form-urlencoded',
                'User-Agent: Mozilla/5.0 (compatible; NganLuong API Client)'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($error) {
            error_log('NganLuong API Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            error_log('NganLuong API HTTP Error: ' . $httpCode);
            return false;
        }

        return $response;
    }

    /**
     * Parse XML response from Ngân Lượng
     */
    private function parseXmlResponse(string $xmlString): array
    {
        $xml = simplexml_load_string($xmlString);

        if ($xml === false) {
            throw new \Exception('Không thể phân tích phản hồi từ Ngân Lượng');
        }

        return [
            'error_code' => (string) $xml->error_code,
            'token' => (string) $xml->token,
            'checkout_url' => (string) $xml->checkout_url,
            'time_limit' => (string) $xml->time_limit,
            'description' => (string) $xml->description
        ];
    }

    /**
     * Validate callback from Ngân Lượng
     */
    public function validateCallback(array $params): bool
    {
        // In a real implementation, you would validate the callback signature
        // For now, just check if required parameters exist
        return isset($params['error_code']) && isset($params['token']);
    }

    /**
     * Get error message by code
     */
    public function getErrorMessage(string $errorCode): string
    {
        $errorMessages = [
            '00' => 'Thành công',
            '99' => 'Lỗi không xác định',
            '01' => 'Phương thức thanh toán không hợp lệ',
            '02' => 'IP của merchant bị từ chối',
            '03' => 'Sai checksum',
            '04' => 'Tên function không hợp lệ',
            '05' => 'Sai version checkout',
            '06' => 'merchant_id không tồn tại hoặc chưa được xác thực',
            '07' => 'Sai merchant_password',
            '08' => 'Thông tin người bán không hợp lệ',
            '09' => 'Tài khoản người bán đang bị đóng băng',
            '10' => 'order_code không hợp lệ',
            '11' => 'Số tiền không hợp lệ',
            '12' => 'Sai đơn vị tiền tệ',
            '13' => 'Mã đơn hàng không thuộc người bán',
            '29' => 'Token không tồn tại',
            '80' => 'Tạo đơn hàng không thành công',
            '81' => 'Đơn hàng chưa được thanh toán',
            '110' => 'receiver_email không hợp lệ',
            '111' => 'receiver_email đã bị khóa',
            '113' => 'Tài khoản không hợp lệ',
            '114' => 'Giao dịch chưa thành công',
            '115' => 'Giao dịch bị hủy',
            '121' => 'Sai định dạng return_url',
            '122' => 'Sai định dạng cancel_url',
            '124' => 'Sai thông tin transation_info',
            '126' => 'Sai định dạng order_description',
            '128' => 'time_limit không hợp lệ',
            '129' => 'buyer_fullname không hợp lệ',
            '130' => 'buyer_email không hợp lệ',
            '131' => 'buyer_mobile không hợp lệ',
            '132' => 'buyer_address không hợp lệ',
            '134' => 'payment_method, bank_code không hợp lệ',
            '135' => 'Lỗi kết nối đến Ngân hàng'
        ];

        return $errorMessages[$errorCode] ?? 'Lỗi không xác định';
    }
}
