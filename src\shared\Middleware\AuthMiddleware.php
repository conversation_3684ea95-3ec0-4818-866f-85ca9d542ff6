<?php

namespace Shared\Middleware;

use Core\Application;

/**
 * Authentication Middleware
 * Checks if user is authenticated before accessing protected routes
 */
class AuthMiddleware
{
    /**
     * Handle the middleware
     */
    public function handle(): bool
    {
        // Basic session validation
        if (!isset($_SESSION['user']) || !isset($_SESSION['access_token'])) {
            $this->redirectToLogin('no_session');
            return false;
        }

        // Check session timeout (2 hours)
        $sessionTimeout = 7200; // 2 hours
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > $sessionTimeout) {
            $this->clearSession();
            $this->redirectToLogin('session_expired');
            return false;
        }

        // Update last activity time
        $_SESSION['last_activity'] = time();

        // Note: User data sync and token validation are now handled automatically
        // in Controller::getUser() on every page load with smart caching

        return true;
    }

    /**
     * Redirect to login page
     */
    private function redirectToLogin(string $reason = 'session_expired'): void
    {
        $app = Application::getInstance();

        // Set appropriate flash message based on reason
        switch ($reason) {
            case 'token_invalid':
                $_SESSION['flash']['warning'] = 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.';
                break;
            case 'session_expired':
                $_SESSION['flash']['warning'] = 'Phiên làm việc đã hết hạn. Vui lòng đăng nhập lại.';
                break;
            default:
                $_SESSION['flash']['warning'] = 'Vui lòng đăng nhập để tiếp tục.';
        }

        $app->redirect('/');
    }

    /**
     * Clear user session
     */
    private function clearSession(): void
    {
        unset($_SESSION['user']);
        unset($_SESSION['access_token']);
        unset($_SESSION['last_activity']);
        unset($_SESSION['last_token_check']);
    }




}
