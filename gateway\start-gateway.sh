#!/bin/bash

echo "🚀 Starting MTF Game Gateway..."

# <PERSON><PERSON><PERSON> thư mục data nếu chưa có
mkdir -p data
mkdir -p letsencrypt

# Tạo network nếu chưa có
docker network create mtfgame-proxy 2>/dev/null || echo "Network mtfgame-proxy already exists"

# Stop existing containers nếu có
echo "🛑 Stopping existing gateway..."
docker-compose down

# Start gateway
echo "🌐 Starting Nginx Proxy Manager..."
docker-compose up -d

# Wait for container to be ready
echo "⏳ Waiting for Nginx Proxy Manager to start..."
sleep 10

# Check status
if docker-compose ps | grep -q "Up"; then
    echo "✅ Gateway started successfully!"
    echo ""
    echo "📋 Access Information:"
    echo "   Admin Interface: http://localhost:81"
    echo "   Default Login:"
    echo "   Email: <EMAIL>"
    echo "   Password: changeme"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Access admin interface at http://localhost:81"
    echo "   2. Change default password"
    echo "   3. Add proxy hosts for your domains"
    echo ""
else
    echo "❌ Failed to start gateway!"
    docker-compose logs
fi
