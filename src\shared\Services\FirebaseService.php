<?php

namespace Shared\Services;

use Core\Application;

/**
 * Firebase Service
 * Handles Firebase Authentication integration
 */
class FirebaseService
{
    private array $config;

    public function __construct()
    {
        $app = Application::getInstance();
        $this->config = $app->getConfig('firebase');
    }

    /**
     * Get Firebase configuration for frontend
     */
    public function getFirebaseConfig(): array
    {
        return [
            'apiKey' => $this->config['api_key'],
            'authDomain' => $this->config['auth_domain'],
            'projectId' => $this->config['project_id'],
            'storageBucket' => $this->config['storage_bucket'],
            'messagingSenderId' => $this->config['messaging_sender_id'],
            'appId' => $this->config['app_id'],
            'measurementId' => $this->config['measurement_id'] ?? null
        ];
    }

    /**
     * Verify Firebase ID Token (server-side verification)
     * Note: This is a simplified version. In production, you should use
     * Firebase Admin SDK for proper token verification
     */
    public function verifyIdToken(string $idToken): array
    {
        // For now, we'll just decode the token without verification
        // In production, use Firebase Admin SDK to verify the token

        $parts = explode('.', $idToken);

        if (count($parts) !== 3) {
            throw new \Exception('Invalid ID token format');
        }

        // Decode payload (second part)
        $payload = json_decode(base64_decode($parts[1]), true);

        if (!$payload) {
            throw new \Exception('Invalid ID token payload');
        }

        // Basic validation
        if (!isset($payload['iss']) || !isset($payload['aud']) || !isset($payload['exp'])) {
            throw new \Exception('Invalid ID token structure');
        }

        // Check if token is expired
        if ($payload['exp'] < time()) {
            throw new \Exception('ID token has expired');
        }

        return [
            'uid' => $payload['sub'] ?? null,
            'email' => $payload['email'] ?? null,
            'email_verified' => $payload['email_verified'] ?? false,
            'name' => $payload['name'] ?? null,
            'picture' => $payload['picture'] ?? null,
            'provider' => $this->getProviderFromToken($payload)
        ];
    }

    /**
     * Get provider from token payload
     */
    private function getProviderFromToken(array $payload): string
    {
        if (isset($payload['firebase']['sign_in_provider'])) {
            return $payload['firebase']['sign_in_provider'];
        }

        // Fallback detection based on issuer or other fields
        if (isset($payload['iss']) && str_contains($payload['iss'], 'google')) {
            return 'google';
        }

        return 'unknown';
    }

    /**
     * Generate JavaScript code for Firebase initialization
     */
    public function getFirebaseInitScript(): string
    {
        $config = json_encode($this->getFirebaseConfig(), JSON_UNESCAPED_SLASHES);

        return "
        // Firebase configuration
        const firebaseConfig = {$config};

        // Initialize Firebase
        if (typeof firebase !== 'undefined') {
            firebase.initializeApp(firebaseConfig);

            // Get Firebase services
            window.auth = firebase.auth();
            window.googleProvider = new firebase.auth.GoogleAuthProvider();
            window.facebookProvider = new firebase.auth.FacebookAuthProvider();

            // Configure providers
            window.googleProvider.addScope('email');
            window.googleProvider.addScope('profile');

            window.facebookProvider.addScope('email');
            window.facebookProvider.addScope('public_profile');
        }
        ";
    }

    /**
     * Get supported OAuth providers
     */
    public function getSupportedProviders(): array
    {
        $app = Application::getInstance();

        // This would typically come from app settings API
        return [
            'google' => $app->getConfig('features.oauth_login'),
            'facebook' => $app->getConfig('features.oauth_login'),
            'apple' => false // Apple sign-in typically for iOS only
        ];
    }
}
