<?php $this->startSection('content'); ?>

<div class="auth-container px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-6 sm:mb-8">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                <i class="fas fa-user-plus text-white text-lg sm:text-xl"></i>
            </div>
            <h1 class="text-2xl sm:text-3xl font-gaming font-bold text-gray-900 mb-2"><?= $__('auth.register.view.title') ?></h1>
            <p class="text-gray-600 text-sm sm:text-base"><?= $__('auth.register.view.subtitle') ?></p>
        </div>

        <div class="auth-form-container rounded-xl p-4 sm:p-6 lg:p-8">
            <!-- OAuth Options -->
            <div class="text-center mb-4 sm:mb-6">
                <p class="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4"><?= $__('auth.register.view.quick_register') ?></p>
                <div class="flex justify-center space-x-3 sm:space-x-4">
                    <button type="button" id="googleRegister" class="w-11 h-11 sm:w-12 sm:h-12 bg-red-500 hover:bg-red-600 text-white rounded-lg flex items-center justify-center transition-all shadow-lg" title="Google">
                        <i class="fab fa-google text-sm sm:text-base"></i>
                    </button>
                    <button type="button" id="facebookRegister" class="w-11 h-11 sm:w-12 sm:h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center justify-center transition-all shadow-lg" title="Facebook">
                        <i class="fab fa-facebook-f text-sm sm:text-base"></i>
                    </button>
                </div>
            </div>

            <div class="relative mb-4 sm:mb-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-xs sm:text-sm">
                    <span class="px-2 sm:px-3 bg-white text-gray-500"><?= $__('auth.register.view.or_register_with_account') ?></span>
                </div>
            </div>

            <!-- Register Form -->
            <form id="registerForm" class="space-y-4 sm:space-y-5" data-type="account">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                <!-- Honeypot field (hidden from users, bots will fill it) -->
                <input type="text" name="website" style="display: none !important;" tabindex="-1" autocomplete="off">

                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-user mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.register.view.username') ?>
                    </label>
                    <div class="relative">
                        <input type="text" name="username" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.register.view.enter_username') ?>" required>
                    </div>
                </div>

                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.register.view.password') ?>
                    </label>
                    <div class="relative">
                        <input type="password" name="password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-16 sm:pr-20 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.register.view.enter_password') ?>" required>
                        <button type="button" class="password-toggle absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 z-10 p-1">
                            <i class="fas fa-eye text-sm"></i>
                        </button>
                    </div>
                </div>

                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.register.view.confirm_password') ?>
                    </label>
                    <div class="relative">
                        <input type="password" name="confirm_password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.register.view.enter_confirm_password') ?>" required>
                    </div>
                </div>

                <!-- Optional Fields Toggle -->
                <div class="text-center">
                    <button type="button" id="toggleOptionalFields" class="text-xs sm:text-sm text-blue-600 hover:text-blue-500 font-medium">
                        <i class="fas fa-plus mr-1"></i>
                        <?= $__('auth.register.view.add_contact_info') ?>
                    </button>
                </div>

                <!-- Optional Fields (Hidden by default) -->
                <div id="optionalFields" class="space-y-4 sm:space-y-6 hidden">
                    <div class="relative">
                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                            <i class="fas fa-envelope mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                            <?= $__('auth.register.view.email') ?>
                        </label>
                        <div class="relative">
                            <input type="email" name="email" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="relative">
                        <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                            <i class="fas fa-phone mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                            <?= $__('auth.register.view.phone') ?>
                        </label>
                        <div class="relative">
                            <input type="tel" name="phone" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="0123456789">
                        </div>
                    </div>
                </div>

                <div class="flex items-start">
                    <input type="checkbox" name="agree_terms" class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5 sm:mt-1" required>
                    <div class="ml-2 sm:ml-3 text-xs sm:text-sm text-gray-700">
                        <span><?= $__('auth.register.view.agree_terms') ?> <a href="<?= $this->url('/terms') ?>" target="_blank" class="text-blue-600 hover:text-blue-500 font-medium"><?= $__('auth.register.view.terms_of_service') ?></a></span>
                    </div>
                </div>

                <!-- Terms Agreement Notice -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-600 mt-0.5 text-sm sm:text-base"></i>
                        </div>
                        <div class="ml-2 sm:ml-3 text-xs sm:text-sm">
                            <p class="text-blue-800 leading-relaxed">
                                <?= $__('auth.register.view.terms_agreement') ?>
                                <a href="<?= $this->url('/terms') ?>" target="_blank" class="font-medium text-blue-600 hover:text-blue-500 underline"><?= $__('auth.register.view.terms_link') ?></a>
                                <?= $__('auth.register.view.and') ?>
                                <a href="<?= $this->url('/privacy') ?>" target="_blank" class="font-medium text-blue-600 hover:text-blue-500 underline"><?= $__('auth.register.view.privacy_link') ?></a>
                                <?= $__('auth.register.view.of_mtfgame') ?>.
                            </p>
                        </div>
                    </div>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-4 rounded-lg font-gaming font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-lg">
                    <span class="btn-text"><?= $__('auth.register.view.create_account') ?></span>
                </button>
            </form>

            <!-- Login Link -->
            <div class="text-center mt-4 sm:mt-6">
                <p class="text-gray-600 text-xs sm:text-sm"><?= $__('auth.register.view.already_have_account') ?>
                    <a href="<?= $this->url('/') ?>" class="text-blue-600 hover:text-blue-500 font-semibold ml-1"><?= $__('auth.register.view.login_now') ?></a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Toggle optional fields
    $('#toggleOptionalFields').click(function() {
        const optionalFields = $('#optionalFields');
        const icon = $(this).find('i');

        if (optionalFields.hasClass('hidden')) {
            optionalFields.removeClass('hidden');
            icon.removeClass('fa-plus').addClass('fa-minus');
            $(this).html('<i class="fas fa-minus mr-1"></i>' + (window.translations?.register_js?.hide_contact_info || 'Ẩn thông tin liên hệ'));
        } else {
            optionalFields.addClass('hidden');
            icon.removeClass('fa-minus').addClass('fa-plus');
            $(this).html('<i class="fas fa-plus mr-1"></i>' + (window.translations?.register_js?.add_contact_info || 'Thêm thông tin liên hệ (tùy chọn)'));
        }
    });

    // Password toggle
    $('.password-toggle').click(function() {
        const input = $(this).siblings('input');
        const icon = $(this).find('i');

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            input.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Account register form
    $('#registerForm').submit(function(e) {
        e.preventDefault();

        // Validate password confirmation
        const password = $('input[name="password"]').val();
        const confirmPassword = $('input[name="confirm_password"]').val();

        if (password !== confirmPassword) {
            GamingApp.showNotification(window.translations?.register_js?.password_mismatch || 'Mật khẩu xác nhận không khớp', 'error');
            return;
        }

        const formData = {
            register_type: 'account',
            username: $('input[name="username"]').val(),
            password: password,
            csrf_token: $('input[name="csrf_token"]').val(),
            email: $('input[name="email"]').val(),
            phone: $('input[name="phone"]').val()
        };

        submitRegister(formData);
    });

    // Google register
    $('#googleRegister').click(function() {
        if (typeof window.auth === 'undefined') {
            GamingApp.showNotification(window.translations?.register_js?.firebase_not_initialized || 'Firebase chưa được khởi tạo', 'error');
            return;
        }

        window.auth.signInWithPopup(window.googleProvider)
            .then((result) => {
                return result.user.getIdToken();
            })
            .then((idToken) => {
                const formData = {
                    register_type: 'google',
                    id_token: idToken
                };
                submitRegister(formData);
            })
            .catch((error) => {
                GamingApp.showNotification((window.translations?.register_js?.google_register_failed || 'Đăng ký Google thất bại') + ': ' + error.message, 'error');
            });
    });

    // Facebook register
    $('#facebookRegister').click(function() {
        if (typeof window.auth === 'undefined') {
            GamingApp.showNotification(window.translations?.register_js?.firebase_not_initialized || 'Firebase chưa được khởi tạo', 'error');
            return;
        }

        window.auth.signInWithPopup(window.facebookProvider)
            .then((result) => {
                return result.user.getIdToken();
            })
            .then((idToken) => {
                const formData = {
                    register_type: 'facebook',
                    id_token: idToken
                };
                submitRegister(formData);
            })
            .catch((error) => {
                GamingApp.showNotification((window.translations?.register_js?.facebook_register_failed || 'Đăng ký Facebook thất bại') + ': ' + error.message, 'error');
            });
    });



    function submitRegister(formData) {
        const submitBtn = $('.gaming-btn-primary:visible');
        const originalText = submitBtn.find('.btn-text, span').text();

        submitBtn.prop('disabled', true).find('.btn-text, span').text(window.translations?.register_js?.creating_account || 'ĐANG TẠO...');

        $.ajax({
            url: '<?= $this->url('/register') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    setTimeout(() => {
                        window.location.href = response.redirect || '<?= $this->url('/') ?>';
                    }, 1000);
                } else {
                    // Response có status false nhưng HTTP status là 200
                    GamingApp.showNotification(response.message, 'error');
                }
                submitBtn.prop('disabled', false).find('.btn-text, span').text(originalText);
            },
            error: function(xhr) {
                let errorMessage = window.translations?.register_js?.error_occurred || 'Đã xảy ra lỗi. Vui lòng thử lại.';

                try {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                    if (response && response.errors) {
                        // Hiển thị validation errors
                        Object.keys(response.errors).forEach(field => {
                            const input = $(`input[name="${field}"]`);
                            input.addClass('border-red-500');
                            GamingApp.showNotification(response.errors[field][0], 'error');
                        });
                        submitBtn.prop('disabled', false).find('.btn-text, span').text(originalText);
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                GamingApp.showNotification(errorMessage, 'error');
                submitBtn.prop('disabled', false).find('.btn-text, span').text(originalText);
            }
        });
    }

    function showSuccess(message) {
        GamingApp.showNotification(message, 'success');
    }

    function showError(message) {
        GamingApp.showNotification(message, 'error');
    }

    function showValidationErrors(errors) {
        Object.keys(errors).forEach(field => {
            const input = $(`input[name="${field}"]`);
            input.addClass('is-invalid');
            if (input.siblings('.invalid-feedback').length === 0) {
                input.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
            }
        });
    }
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
