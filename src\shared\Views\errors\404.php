<?php $this->startSection('content'); ?>

<div class="error-container">
    <div class="error-content">
        <div class="error-animation">
            <div class="error-code">404</div>
            <div class="error-glitch" data-text="404">404</div>
        </div>

        <h1 class="error-title"><?= $__('shared.errors.page_not_found') ?></h1>
        <p class="error-message">
            <?= $this->escape($message ?? $__('shared.errors.page_not_found_message')) ?>
        </p>

        <div class="error-actions">
            <a href="<?= $this->url('/') ?>" class="btn gaming-btn gaming-btn-primary">
                <i class="fas fa-home"></i>
                <?= $__('shared.header.home') ?>
            </a>
            <button onclick="history.back()" class="btn gaming-btn gaming-btn-secondary">
                <i class="fas fa-arrow-left"></i>
                <?= $__('shared.errors.back') ?>
            </button>
        </div>
    </div>
</div>

<style>
.error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.error-content {
    max-width: 600px;
}

.error-animation {
    position: relative;
    margin-bottom: 2rem;
}

.error-code {
    font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace;
    font-size: 8rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 50px rgba(255, 107, 53, 0.5);
    animation: pulse 2s ease-in-out infinite;
}

.error-glitch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace;
    font-size: 8rem;
    font-weight: 900;
    color: var(--primary-color);
    opacity: 0;
    animation: glitch 3s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glitch {
    0%, 90%, 100% { opacity: 0; }
    5% {
        opacity: 1;
        transform: translateX(-50%) skew(-2deg);
        filter: hue-rotate(90deg);
    }
    10% {
        opacity: 1;
        transform: translateX(-50%) skew(2deg);
        filter: hue-rotate(180deg);
    }
    15% { opacity: 0; }
}

.error-title {
    font-family: 'JetBrains Mono', Consolas, Monaco, 'Courier New', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
}

.error-message {
    color: var(--text-muted);
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.gaming-btn-secondary {
    background: linear-gradient(45deg, var(--surface-color), rgba(255, 255, 255, 0.1));
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.gaming-btn-secondary:hover {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), var(--surface-color));
    border-color: var(--primary-color);
    color: var(--text-color);
}

@media (max-width: 768px) {
    .error-code, .error-glitch {
        font-size: 5rem;
    }

    .error-title {
        font-size: 2rem;
    }

    .error-actions {
        flex-direction: column;
        align-items: center;
    }

    .gaming-btn {
        width: 200px;
    }
}
</style>

<?php $this->endSection(); ?>

<!-- Include the layout -->
<?php include __DIR__ . '/../layouts/app.php'; ?>
