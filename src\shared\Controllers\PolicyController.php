<?php

namespace Shared\Controllers;

use Core\Controller;

/**
 * Shared Policy Controller
 * Handles legal pages for both ID and Pay domains
 */
class PolicyController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Translation helper function
     */
    private function __($key)
    {
        return $this->languageHelper->translate($key);
    }

    /**
     * Terms of Service page
     */
    public function terms(): void
    {
        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('terms');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('policy/terms', array_merge($this->getThemeData(), [
            'title' => $this->__('policy.terms.view.title'),
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Privacy Policy page
     */
    public function privacy(): void
    {
        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('privacy');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('policy/privacy', array_merge($this->getThemeData(), [
            'title' => $this->__('policy.privacy.view.title'),
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Contact Us page
     */
    public function contact(): void
    {
        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('contact');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        // Generate CSRF token for contact form
        $csrf_token = $this->securityHelper->generateCsrfToken();

        $this->render('policy/contact', array_merge($this->getThemeData(), [
            'title' => $this->__('policy.contact.view.title'),
            'csrf_token' => $csrf_token,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Handle contact form submission
     */
    public function submitContact(): void
    {
        try {
            // Validate CSRF token
            if (!$this->securityHelper->verifyCsrfToken($this->input('csrf_token'))) {
                $this->jsonResponse([
                    'status' => false,
                    'message' => $this->__('shared.validation.csrf_invalid')
                ], 400);
                return;
            }

            // Get form data
            $name = trim($this->input('name'));
            $email = trim($this->input('email'));
            $subject = trim($this->input('subject'));
            $message = trim($this->input('message'));

            // Validation
            $errors = [];

            if (empty($name)) {
                $errors['name'] = [$this->__('policy.contact.controller.name_required')];
            }

            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = [$this->__('policy.contact.controller.email_invalid')];
            }

            if (empty($subject)) {
                $errors['subject'] = [$this->__('policy.contact.controller.subject_required')];
            }

            if (empty($message)) {
                $errors['message'] = [$this->__('policy.contact.controller.message_required')];
            }

            if (!empty($errors)) {
                $this->jsonResponse([
                    'status' => false,
                    'message' => $this->__('shared.validation.check_errors'),
                    'errors' => $errors
                ], 422);
                return;
            }

            // Here you would typically send email or save to database
            // For now, just return success
            $this->jsonResponse([
                'status' => true,
                'message' => $this->__('policy.contact.controller.message_sent')
            ]);

        } catch (\Exception $e) {
            error_log('Contact form error: ' . $e->getMessage());

            $this->jsonResponse([
                'status' => false,
                'message' => $this->__('shared.validation.error_occurred')
            ], 500);
        }
    }
}
