<?php $this->startSection('content'); ?>

<div class="auth-container px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-6 sm:mb-8">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                <i class="fas fa-user text-white text-lg sm:text-xl"></i>
            </div>
            <h1 class="text-2xl sm:text-3xl font-gaming font-bold text-gray-900 mb-2"><?= $__('auth.login.view.title') ?></h1>
            <p class="text-gray-600 text-sm sm:text-base"><?= $__('auth.login.view.subtitle') ?></p>
        </div>

        <div class="auth-form-container rounded-xl p-4 sm:p-6 lg:p-8">
            <!-- <PERSON><PERSON>uth <PERSON>gin Options -->
            <div class="text-center mb-4 sm:mb-6">
                <p class="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4"><?= $__('auth.login.view.quick_login') ?></p>
                <div class="flex justify-center space-x-3 sm:space-x-4">
                    <button type="button" id="googleLogin" class="w-11 h-11 sm:w-12 sm:h-12 bg-red-500 hover:bg-red-600 text-white rounded-lg flex items-center justify-center transition-all shadow-lg" title="Google">
                        <i class="fab fa-google text-sm sm:text-base"></i>
                    </button>
                    <button type="button" id="facebookLogin" class="w-11 h-11 sm:w-12 sm:h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center justify-center transition-all shadow-lg" title="Facebook">
                        <i class="fab fa-facebook-f text-sm sm:text-base"></i>
                    </button>
                </div>
            </div>

            <div class="relative mb-4 sm:mb-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-xs sm:text-sm">
                    <span class="px-2 sm:px-3 bg-white text-gray-500"><?= $__('auth.login.view.or_login_with_account') ?></span>
                </div>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-4 sm:space-y-5" data-type="account">
                <?php if (isset($return_url)): ?>
                    <input type="hidden" name="return_url" value="<?= htmlspecialchars($return_url) ?>">
                <?php endif; ?>

                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                <!-- Honeypot field (hidden from users, bots will fill it) -->
                <input type="text" name="website" style="display: none !important;" tabindex="-1" autocomplete="off">
                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-user mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.login.view.username') ?>
                    </label>
                    <div class="relative">
                        <input type="text" name="username" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-rose-500 focus:border-rose-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.login.view.enter_username') ?>" required>
                    </div>
                </div>

                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-lock mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.login.view.password') ?>
                    </label>
                    <div class="relative">
                        <input type="password" name="password" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-16 sm:pr-20 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-rose-500 focus:border-rose-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.login.view.enter_password') ?>" required>
                        <button type="button" class="password-toggle absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 z-10 p-1">
                            <i class="fas fa-eye text-sm"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between text-xs sm:text-sm">
                    <label class="flex items-center text-gray-700">
                        <input type="checkbox" name="remember" class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-1.5 sm:mr-2">
                        <span class="select-none"><?= $__('auth.login.view.remember_me') ?></span>
                    </label>
                    <a href="<?= $this->url('/forgot-password') ?>" class="text-blue-600 hover:text-blue-500 font-medium"><?= $__('auth.login.view.forgot_password') ?>?</a>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-4 rounded-lg font-gaming font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-lg">
                    <span class="btn-text"><?= $__('shared.login_js.login_button') ?></span>
                </button>
            </form>

            <!-- Register Link -->
            <div class="text-center mt-4 sm:mt-6">
                <p class="text-gray-600 text-xs sm:text-sm"><?= $__('auth.login.view.dont_have_account') ?>
                    <a href="<?= $this->url('/register') ?>" class="text-blue-600 hover:text-blue-500 font-semibold ml-1"><?= $__('auth.login.view.create_account') ?></a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Custom checkbox functionality
    $('input[name="remember"]').change(function() {
        const checkbox = $(this);
        const customCheckbox = checkbox.siblings('div').last();

        if (checkbox.is(':checked')) {
            customCheckbox.removeClass('opacity-0').addClass('opacity-100');
        } else {
            customCheckbox.removeClass('opacity-100').addClass('opacity-0');
        }
    });

    // Password toggle
    $('.password-toggle').click(function() {
        const input = $(this).siblings('input');
        const icon = $(this).find('i');

        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            input.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    // Account login form
    $('#loginForm').submit(function(e) {
        e.preventDefault();

        const formData = {
            login_type: 'account',
            username: $('input[name="username"]').val(),
            password: $('input[name="password"]').val(),
            csrf_token: $('input[name="csrf_token"]').val(),
            return_url: $('input[name="return_url"]').val()
        };

        submitLogin(formData);
    });

    // Google login
    $('#googleLogin').click(function() {
        if (typeof window.auth === 'undefined') {
            GamingApp.showNotification(window.translations?.login_js?.firebase_not_initialized || 'Firebase chưa được khởi tạo', 'error');
            return;
        }

        window.auth.signInWithPopup(window.googleProvider)
            .then((result) => {
                return result.user.getIdToken();
            })
            .then((idToken) => {
                const formData = {
                    login_type: 'google',
                    id_token: idToken
                };
                submitLogin(formData);
            })
            .catch((error) => {
                GamingApp.showNotification((window.translations?.login_js?.google_login_failed || 'Đăng nhập Google thất bại') + ': ' + error.message, 'error');
            });
    });

    // Facebook login
    $('#facebookLogin').click(function() {
        if (typeof window.auth === 'undefined') {
            GamingApp.showNotification(window.translations?.login_js?.firebase_not_initialized || 'Firebase chưa được khởi tạo', 'error');
            return;
        }

        window.auth.signInWithPopup(window.facebookProvider)
            .then((result) => {
                return result.user.getIdToken();
            })
            .then((idToken) => {
                const formData = {
                    login_type: 'facebook',
                    id_token: idToken
                };
                submitLogin(formData);
            })
            .catch((error) => {
                GamingApp.showNotification((window.translations?.login_js?.facebook_login_failed || 'Đăng nhập Facebook thất bại') + ': ' + error.message, 'error');
            });
    });



    function submitLogin(formData) {
        const submitBtn = $('#loginForm button[type="submit"]');
        submitBtn.prop('disabled', true).find('.btn-text').text(window.translations?.login_js?.logging_in || 'ĐANG ĐĂNG NHẬP...');

        $.ajax({
            url: '<?= $this->url('/') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    setTimeout(() => {
                        window.location.href = response.redirect || '<?= $this->url('/') ?>';
                    }, 1000);
                } else {
                    // Response có status false nhưng HTTP status là 200
                    GamingApp.showNotification(response.message, 'error');
                }
                submitBtn.prop('disabled', false).find('.btn-text').text(window.translations?.login_js?.login || 'ĐĂNG NHẬP');
            },
            error: function(xhr) {
                let errorMessage = window.translations?.login_js?.error_occurred || 'Đã xảy ra lỗi. Vui lòng thử lại.';

                try {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                    if (response && response.errors) {
                        // Hiển thị validation errors
                        Object.keys(response.errors).forEach(field => {
                            const input = $(`input[name="${field}"]`);
                            input.addClass('border-red-500');
                            GamingApp.showNotification(response.errors[field][0], 'error');
                        });
                        submitBtn.prop('disabled', false).find('.btn-text').text(window.translations?.login_js?.login || 'ĐĂNG NHẬP');
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                GamingApp.showNotification(errorMessage, 'error');
                submitBtn.prop('disabled', false).find('.btn-text').text(window.translations?.login_js?.login || 'ĐĂNG NHẬP');
            }
        });
    }

    function showSuccess(message) {
        // Implementation for success notification
        console.log('Success:', message);
    }

    function showError(message) {
        // Implementation for error notification
        console.log('Error:', message);
    }

    function showValidationErrors(errors) {
        // Implementation for validation errors
        console.log('Validation errors:', errors);
    }
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
