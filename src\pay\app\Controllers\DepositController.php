<?php

namespace Pay\Controllers;

use Core\Controller;
use Core\ValidationException;
use Shared\Services\ApiService;
use Shared\Services\NganLuongService;

/**
 * Deposit Controller for Pay Domain
 * Handles deposit functionality with Ngân Lượng integration
 */
class DepositController extends Controller
{
    private ApiService $apiService;
    private NganLuongService $nganLuongService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();
        $this->nganLuongService = new NganLuongService();

        // Override view to use Pay domain views
        $payViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($payViewPath);
    }

    /**
     * Show deposit page
     */
    public function index(): void
    {
        // Check authentication first - redirect to id domain if not logged in
        if (!$this->isAuthenticated()) {
            $this->redirectToIdDomain('/deposit');
            return;
        }

        $user = $this->getUser();

        // Get available payment methods
        $paymentMethods = $this->getPaymentMethods();

        // Get deposit packages
        $packages = $this->getDepositPackages();

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('deposit');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('deposit/index', array_merge($this->getThemeData(), [
            'title' => 'Nạp tiền',
            'user' => $user,
            'payment_methods' => $paymentMethods,
            'packages' => $packages,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Process deposit request
     */
    public function process(): void
    {
        // Set content type to JSON
        header('Content-Type: application/json');

        // Check authentication first
        if (!$this->isAuthenticated()) {
            $this->json([
                'status' => false,
                'message' => 'Vui lòng đăng nhập để thực hiện giao dịch',
                'redirect' => $this->getLoginUrl($this->getPageUrl('deposit'))
            ], 401);
            return;
        }

        try {
            $data = $this->validate([
                'amount' => 'required',
                'payment_method' => '',
                'bank_code' => '',
                'allow_method_selection' => ''
            ]);

            // Validate amount
            $amount = (int) $data['amount'];
            if ($amount < 2000) {
                throw new \Exception('Số tiền nạp tối thiểu là 2,000 VNĐ');
            }

            if ($amount > ********) {
                throw new \Exception('Số tiền nạp tối đa là 10,000,000 VNĐ');
            }

            // Check if user wants to select payment method on Ngân Lượng page
            $allowMethodSelection = !empty($data['allow_method_selection']);

            if (!$allowMethodSelection) {
                // Validate payment method if pre-selected
                if (empty($data['payment_method'])) {
                    throw new \Exception('Vui lòng chọn phương thức thanh toán');
                }

                $paymentMethods = $this->nganLuongService->getPaymentMethods();
                if (!isset($paymentMethods[$data['payment_method']])) {
                    throw new \Exception('Phương thức thanh toán không hợp lệ');
                }

                $method = $paymentMethods[$data['payment_method']];
                if ($amount < $method['min_amount']) {
                    throw new \Exception("Số tiền tối thiểu cho phương thức này là " . number_format($method['min_amount']) . " VNĐ");
                }
            }

            // Process deposit with Ngân Lượng
            $result = $this->processDeposit($data, $allowMethodSelection);

            $this->json([
                'status' => true,
                'message' => 'Đơn hàng đã được tạo thành công',
                'data' => $result
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get available payment methods from Ngân Lượng
     */
    private function getPaymentMethods(): array
    {
        $nganLuongMethods = $this->nganLuongService->getPaymentMethods();
        $methods = [];

        // Convert Ngân Lượng methods to our format
        foreach ($nganLuongMethods as $key => $method) {
            $methods[$key] = [
                'name' => $method['name'],
                'description' => $method['description'],
                'icon' => $method['icon'],
                'enabled' => true,
                'fee' => 0,
                'min_amount' => $method['min_amount'],
                'banks' => $method['banks'] ?? []
            ];
        }

        return $methods;
    }

    /**
     * Get deposit packages
     */
    private function getDepositPackages(): array
    {
        $packages = [
            ['amount' => 20000, 'bonus_percent' => 0],
            ['amount' => 50000, 'bonus_percent' => 0],
            ['amount' => 100000, 'bonus_percent' => 0],
            ['amount' => 200000, 'bonus_percent' => 0],
            ['amount' => 300000, 'bonus_percent' => 0],
            ['amount' => 400000, 'bonus_percent' => 0],
            ['amount' => 500000, 'bonus_percent' => 0],
            ['amount' => 1000000, 'bonus_percent' => 0],
            ['amount' => 2000000, 'bonus_percent' => 0],
            ['amount' => 3000000, 'bonus_percent' => 0],
            ['amount' => 4000000, 'bonus_percent' => 0],
            ['amount' => 5000000, 'bonus_percent' => 0],
            ['amount' => ********, 'bonus_percent' => 0],
        ];

        // Calculate rice amount for each package (10k VND = 1000 rice)
        foreach ($packages as &$package) {
            $package['rice_amount'] = ($package['amount'] / 10) + ($package['amount'] / 10 * $package['bonus_percent'] / 100);
            $package['bonus_rice'] = $package['amount'] / 10 * $package['bonus_percent'] / 100;
        }

        return $packages;
    }

    /**
     * Process deposit with Ngân Lượng integration
     */
    private function processDeposit(array $data, bool $allowMethodSelection = false): array
    {
        $user = $this->getUser();

        // Generate unique order code
        $orderCode = 'DEP' . date('YmdHis') . rand(1000, 9999);

        // Build URLs
        $baseUrl = $this->getDomainUrl('pay');
        $returnUrl = $baseUrl . '/deposit/success';
        $cancelUrl = $baseUrl . '/deposit/cancel';
        $notifyUrl = $baseUrl . '/deposit/notify';

        // Prepare order data for Ngân Lượng
        $orderData = [
            'order_code' => $orderCode,
            'total_amount' => (int) $data['amount'],
            'order_description' => "Nạp tiền vào tài khoản {$user['username']} - {$orderCode}",
            'return_url' => $returnUrl,
            'notify_url' => $notifyUrl,
            'cancel_url' => $cancelUrl,
            'time_limit' => 1440, // 24 hours
            'buyer_fullname' => $user['username'],
            'buyer_email' => $user['email'] ?? '<EMAIL>',
            'buyer_mobile' => $user['phone'] ?? '**********',
            'buyer_address' => 'Việt Nam'
        ];

        // Add payment method and bank code only if not allowing method selection
        if (!$allowMethodSelection) {
            $orderData['payment_method'] = $data['payment_method'];
            $orderData['bank_code'] = $data['bank_code'] ?? '';
        }

        try {
            // Create order with Ngân Lượng
            $response = $this->nganLuongService->createOrder($orderData, $allowMethodSelection);

            if ($response['error_code'] !== '00') {
                $errorMessage = $this->nganLuongService->getErrorMessage($response['error_code']);
                throw new \Exception("Lỗi tạo đơn hàng: {$errorMessage}");
            }

            // Save order to database (you would implement this)
            // $this->saveOrderToDatabase($orderCode, $user['user_id'], $data, $response['token']);

            return [
                'order_code' => $orderCode,
                'token' => $response['token'],
                'checkout_url' => $response['checkout_url'],
                'time_limit' => $response['time_limit'],
                'amount' => (int) $data['amount'],
                'payment_method' => $data['payment_method'],
                'bank_code' => $data['bank_code'] ?? '',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            error_log('Ngân Lượng Error: ' . $e->getMessage());
            throw new \Exception('Không thể tạo đơn hàng thanh toán. Vui lòng thử lại sau.');
        }
    }

    /**
     * Get payment instructions based on method
     */
    private function getPaymentInstructions(string $method, string $transactionId): array
    {
        switch ($method) {
            case 'bank':
                return [
                    'title' => 'Hướng dẫn chuyển khoản',
                    'steps' => [
                        'Chuyển khoản đến số tài khoản: **********',
                        'Tên tài khoản: CONG TY GAME HUB',
                        'Ngân hàng: Vietcombank',
                        'Nội dung chuyển khoản: ' . $transactionId,
                        'Sau khi chuyển khoản, hệ thống sẽ tự động cộng tiền trong 5-10 phút'
                    ]
                ];

            case 'momo':
                return [
                    'title' => 'Hướng dẫn thanh toán MoMo',
                    'steps' => [
                        'Mở ứng dụng MoMo',
                        'Chọn "Chuyển tiền" -> "Đến số điện thoại"',
                        'Nhập số điện thoại: **********',
                        'Nhập nội dung: ' . $transactionId,
                        'Xác nhận chuyển tiền'
                    ]
                ];

            default:
                return [
                    'title' => 'Hướng dẫn thanh toán',
                    'steps' => [
                        'Làm theo hướng dẫn trên màn hình',
                        'Hoàn tất thanh toán',
                        'Chờ hệ thống xử lý'
                    ]
                ];
        }
    }

    /**
     * Handle successful payment return from Ngân Lượng
     */
    public function success(): void
    {
        $token = $_GET['token'] ?? '';
        $errorCode = $_GET['error_code'] ?? '';

        if (empty($token)) {
            $this->redirect('/deposit?error=invalid_token');
            return;
        }

        try {
            // Check order status with Ngân Lượng
            $orderStatus = $this->nganLuongService->checkOrderStatus($token);

            if ($orderStatus['error_code'] === '00' && $orderStatus['data']['transaction_status'] === '00') {
                // Payment successful
                $this->render('deposit/success', array_merge($this->getThemeData(), [
                    'title' => 'Thanh toán thành công',
                    'order_data' => $orderStatus['data']
                ]));
            } else {
                // Payment failed or pending
                $this->render('deposit/failed', array_merge($this->getThemeData(), [
                    'title' => 'Thanh toán thất bại',
                    'error_message' => $this->nganLuongService->getErrorMessage($orderStatus['error_code'])
                ]));
            }

        } catch (\Exception $e) {
            error_log('Payment verification error: ' . $e->getMessage());
            $this->redirect('/deposit?error=verification_failed');
        }
    }

    /**
     * Handle cancelled payment from Ngân Lượng
     */
    public function cancel(): void
    {
        $this->render('deposit/cancel', array_merge($this->getThemeData(), [
            'title' => 'Thanh toán bị hủy'
        ]));
    }

    /**
     * Handle payment notification from Ngân Lượng (server-to-server)
     */
    public function notify(): void
    {
        header('Content-Type: text/plain');

        $token = $_POST['token'] ?? $_GET['token'] ?? '';
        $errorCode = $_POST['error_code'] ?? $_GET['error_code'] ?? '';

        if (empty($token)) {
            echo 'INVALID_TOKEN';
            return;
        }

        try {
            // Validate callback
            if (!$this->nganLuongService->validateCallback($_POST ?: $_GET)) {
                echo 'INVALID_CALLBACK';
                return;
            }

            // Check order status
            $orderStatus = $this->nganLuongService->checkOrderStatus($token);

            if ($orderStatus['error_code'] === '00' && $orderStatus['data']['transaction_status'] === '00') {
                // Payment successful - update database
                // $this->updateOrderStatus($orderStatus['data']);

                // You would implement this to:
                // 1. Update order status in database
                // 2. Add balance to user account
                // 3. Send notification to user

                echo 'OK';
            } else {
                echo 'PAYMENT_FAILED';
            }

        } catch (\Exception $e) {
            error_log('Payment notification error: ' . $e->getMessage());
            echo 'ERROR';
        }
    }

    /**
     * Check order status (AJAX endpoint)
     */
    public function checkStatus(): void
    {
        header('Content-Type: application/json');

        $token = $this->input('token');
        if (empty($token)) {
            $this->json([
                'status' => false,
                'message' => 'Token không hợp lệ'
            ], 400);
            return;
        }

        try {
            $orderStatus = $this->nganLuongService->checkOrderStatus($token);

            $this->json([
                'status' => true,
                'data' => $orderStatus
            ]);

        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => 'Không thể kiểm tra trạng thái đơn hàng'
            ], 500);
        }
    }

    /**
     * Redirect to ID domain for authentication
     */
    private function redirectToIdDomain(string $returnPath): void
    {
        // Build return URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $returnUrl = urlencode($protocol . '://' . $host . $returnPath);

        // Use Controller's method to get login URL
        $loginUrl = $this->getLoginUrl($returnUrl);

        header("Location: " . $loginUrl);
        exit;
    }
}
