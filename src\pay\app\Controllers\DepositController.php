<?php

namespace Pay\Controllers;

use Core\Controller;
use Core\ValidationException;
use Shared\Services\ApiService;

/**
 * Deposit Controller for Pay Domain
 * Handles deposit functionality
 */
class DepositController extends Controller
{
    private ApiService $apiService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();

        // Override view to use Pay domain views
        $payViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($payViewPath);
    }

    /**
     * Show deposit page
     */
    public function index(): void
    {
        // Check authentication first - redirect to id domain if not logged in
        if (!$this->isAuthenticated()) {
            $this->redirectToIdDomain('/deposit');
            return;
        }

        $user = $this->getUser();

        // Get available payment methods
        $paymentMethods = $this->getPaymentMethods();

        // Get deposit packages
        $packages = $this->getDepositPackages();

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('deposit');
        $structuredData = $this->seoHelper->generateStructuredData('organization');

        $this->render('deposit/index', array_merge($this->getThemeData(), [
            'title' => 'Nạp tiền',
            'user' => $user,
            'payment_methods' => $paymentMethods,
            'packages' => $packages,
            'seoData' => $seoData,
            'structuredData' => $structuredData
        ]));
    }

    /**
     * Process deposit request
     */
    public function process(): void
    {
        // Set content type to JSON
        header('Content-Type: application/json');

        // Check authentication first
        if (!$this->isAuthenticated()) {
            $this->json([
                'status' => false,
                'message' => 'Vui lòng đăng nhập để thực hiện giao dịch',
                'redirect' => $this->getLoginUrl($this->getPageUrl('deposit'))
            ], 401);
            return;
        }

        try {
            $data = $this->validate([
                'amount' => 'required',
                'payment_method' => 'required'
            ]);

            // Validate amount
            $amount = (int) $data['amount'];
            if ($amount < 20000) {
                throw new \Exception('Số tiền nạp tối thiểu là 20,000 VNĐ');
            }

            if ($amount > 10000000) {
                throw new \Exception('Số tiền nạp tối đa là 10,000,000 VNĐ');
            }

            // Validate amount must be multiple of 10,000
            if ($amount % 10000 !== 0) {
                throw new \Exception('Số tiền nạp phải là bội số của 10,000 VNĐ');
            }

            // Mock deposit processing (in real app, call payment API)
            $result = $this->processDeposit($data);

            $this->json([
                'status' => true,
                'message' => $this->languageHelper->translate('deposit.controller.success'),
                'data' => $result
            ]);

        } catch (ValidationException $e) {
            $this->json([
                'status' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $e->getErrors()
            ], 422);
        } catch (\Exception $e) {
            $this->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get available payment methods
     */
    private function getPaymentMethods(): array
    {
        return [
            'bank' => [
                'name' => 'Chuyển khoản ngân hàng',
                'description' => 'Chuyển khoản qua ATM/Internet Banking',
                'icon' => 'fas fa-university',
                'enabled' => true,
                'fee' => 0
            ],
            'momo' => [
                'name' => 'Ví MoMo',
                'description' => 'Thanh toán qua ví điện tử MoMo',
                'icon' => 'fas fa-mobile-alt',
                'enabled' => true,
                'fee' => 0
            ]
        ];
    }

    /**
     * Get deposit packages
     */
    private function getDepositPackages(): array
    {
        $packages = [
            ['amount' => 20000, 'bonus_percent' => 0],
            ['amount' => 50000, 'bonus_percent' => 0],
            ['amount' => 100000, 'bonus_percent' => 0],
            ['amount' => 200000, 'bonus_percent' => 0],
            ['amount' => 300000, 'bonus_percent' => 0],
            ['amount' => 400000, 'bonus_percent' => 0],
            ['amount' => 500000, 'bonus_percent' => 0],
            ['amount' => 1000000, 'bonus_percent' => 0],
            ['amount' => 2000000, 'bonus_percent' => 0],
            ['amount' => 3000000, 'bonus_percent' => 0],
            ['amount' => 4000000, 'bonus_percent' => 0],
            ['amount' => 5000000, 'bonus_percent' => 0],
            ['amount' => 10000000, 'bonus_percent' => 0],
        ];

        // Calculate rice amount for each package (10k VND = 1000 rice)
        foreach ($packages as &$package) {
            $package['rice_amount'] = ($package['amount'] / 10) + ($package['amount'] / 10 * $package['bonus_percent'] / 100);
            $package['bonus_rice'] = $package['amount'] / 10 * $package['bonus_percent'] / 100;
        }

        return $packages;
    }

    /**
     * Process deposit (mock implementation)
     */
    private function processDeposit(array $data): array
    {
        $user = $this->getUser();

        // Generate transaction ID
        $transactionId = 'DEP' . date('YmdHis') . rand(1000, 9999);

        // Mock deposit data
        return [
            'transaction_id' => $transactionId,
            'user_id' => $user['user_id'],
            'amount' => (int) $data['amount'],
            'payment_method' => $data['payment_method'],
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s'),
            'instructions' => $this->getPaymentInstructions($data['payment_method'], $transactionId)
        ];
    }

    /**
     * Get payment instructions based on method
     */
    private function getPaymentInstructions(string $method, string $transactionId): array
    {
        switch ($method) {
            case 'bank':
                return [
                    'title' => 'Hướng dẫn chuyển khoản',
                    'steps' => [
                        'Chuyển khoản đến số tài khoản: **********',
                        'Tên tài khoản: CONG TY GAME HUB',
                        'Ngân hàng: Vietcombank',
                        'Nội dung chuyển khoản: ' . $transactionId,
                        'Sau khi chuyển khoản, hệ thống sẽ tự động cộng tiền trong 5-10 phút'
                    ]
                ];

            case 'momo':
                return [
                    'title' => 'Hướng dẫn thanh toán MoMo',
                    'steps' => [
                        'Mở ứng dụng MoMo',
                        'Chọn "Chuyển tiền" -> "Đến số điện thoại"',
                        'Nhập số điện thoại: **********',
                        'Nhập nội dung: ' . $transactionId,
                        'Xác nhận chuyển tiền'
                    ]
                ];

            default:
                return [
                    'title' => 'Hướng dẫn thanh toán',
                    'steps' => [
                        'Làm theo hướng dẫn trên màn hình',
                        'Hoàn tất thanh toán',
                        'Chờ hệ thống xử lý'
                    ]
                ];
        }
    }

    /**
     * Redirect to ID domain for authentication
     */
    private function redirectToIdDomain(string $returnPath): void
    {
        // Build return URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $returnUrl = urlencode($protocol . '://' . $host . $returnPath);

        // Use Controller's method to get login URL
        $loginUrl = $this->getLoginUrl($returnUrl);

        header("Location: " . $loginUrl);
        exit;
    }
}
