# ------------------------------------------------------------
# id.mtfgame.com, pay.mtfgame.com
# ------------------------------------------------------------



map $scheme $hsts_header {
    https   "max-age=63072000; preload";
}

server {
  set $forward_scheme http;
  set $server         "host.docker.internal";
  set $port           8080;

  listen 80;
#listen [::]:80;


  server_name id.mtfgame.com pay.mtfgame.com;
http2 off;












  access_log /data/logs/proxy-host-3_access.log proxy;
  error_log /data/logs/proxy-host-3_error.log warn;







  location / {






    

    # Proxy!
    include conf.d/include/proxy.conf;
  }


  # Custom
  include /data/nginx/custom/server_proxy[.]conf;
}
