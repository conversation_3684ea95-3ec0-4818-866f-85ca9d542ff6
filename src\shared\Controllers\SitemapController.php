<?php

namespace Shared\Controllers;

use Core\Controller;
use Shared\Helpers\DomainHelper;

/**
 * Sitemap Controller
 * Generates XML sitemaps for both domains
 */
class SitemapController extends Controller
{
    /**
     * Generate sitemap for current domain
     */
    public function index(): void
    {
        $currentDomain = DomainHelper::getCurrentDomain();
        $baseUrl = DomainHelper::getDomain($currentDomain);

        // Set XML content type
        header('Content-Type: application/xml; charset=utf-8');

        // Generate sitemap based on domain
        if ($currentDomain === 'id') {
            $this->generateIdSitemap($baseUrl);
        } else {
            $this->generatePaySitemap($baseUrl);
        }
    }

    /**
     * Generate sitemap for ID domain
     */
    private function generateIdSitemap(string $baseUrl): void
    {
        $urls = [
            [
                'loc' => $baseUrl . '/',
                'changefreq' => 'daily',
                'priority' => '1.0',
                'lastmod' => date('Y-m-d')
            ],
            [
                'loc' => $baseUrl . '/register',
                'changefreq' => 'monthly',
                'priority' => '0.8',
                'lastmod' => date('Y-m-d')
            ],
            [
                'loc' => $baseUrl . '/forgot-password',
                'changefreq' => 'monthly',
                'priority' => '0.6',
                'lastmod' => date('Y-m-d')
            ]
        ];

        $this->outputSitemap($urls);
    }

    /**
     * Generate sitemap for Pay domain
     */
    private function generatePaySitemap(string $baseUrl): void
    {
        $urls = [
            [
                'loc' => $baseUrl . '/',
                'changefreq' => 'daily',
                'priority' => '1.0',
                'lastmod' => date('Y-m-d')
            ],
            [
                'loc' => $baseUrl . '/deposit',
                'changefreq' => 'weekly',
                'priority' => '0.9',
                'lastmod' => date('Y-m-d')
            ]
        ];

        // Add game guide pages
        $games = $this->getAvailableGames();
        foreach ($games as $game) {
            $urls[] = [
                'loc' => $baseUrl . '/game/' . $game['id'] . '/guide',
                'changefreq' => 'monthly',
                'priority' => '0.7',
                'lastmod' => date('Y-m-d')
            ];
        }

        $this->outputSitemap($urls);
    }

    /**
     * Get available games for sitemap
     */
    private function getAvailableGames(): array
    {
        return [
            [
                'id' => 'lua_game',
                'name' => 'Nạp Lúa'
            ],
            [
                'id' => 'adventure_game',
                'name' => 'Adventure Quest'
            ]
        ];
    }

    /**
     * Output XML sitemap
     */
    private function outputSitemap(array $urls): void
    {
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            echo '  <url>' . "\n";
            echo '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
            echo '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            echo '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            echo '    <priority>' . $url['priority'] . '</priority>' . "\n";
            echo '  </url>' . "\n";
        }

        echo '</urlset>' . "\n";
    }
}
