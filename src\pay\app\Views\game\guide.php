<?php $this->startSection('content'); ?>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-2">
            <li class="inline-flex items-center">
                <a href="<?= $this->url('/') ?>" class="text-sm text-gray-600 hover:text-blue-600">
                    <i class="fas fa-home mr-1"></i>
                    <?= $__('game_guide.view.breadcrumb_home') ?>
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                    <span class="text-sm text-gray-500"><?= $__('game_guide.view.breadcrumb_deposit') ?></span>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-1"></i>
                    <span class="text-sm text-gray-700"><?= $this->escape($game['name']) ?></span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Game Header -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div class="flex items-center">
            <div class="w-12 h-12 rounded-lg overflow-hidden mr-3">
                <img src="<?= $this->asset('images/' . $game['image']) ?>" alt="<?= $this->escape($game['name']) ?>" class="w-full h-full object-cover">
            </div>
            <div>
                <h1 class="text-xl font-semibold text-gray-900">
                    <?= str_replace('{game_name}', $this->escape($game['name']), $__('game_guide.view.title')) ?>
                </h1>
                <p class="text-sm text-gray-600"><?= $this->escape($game['description']) ?></p>
            </div>
        </div>
    </div>

    <!-- Steps -->
    <div class="space-y-6">
        <!-- Step 1 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-4 mt-1">1</div>
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">
                        <?= $__('game_guide.view.step_1_title') ?>
                    </h3>
                    <div class="flex items-center justify-center py-4">
                        <div class="w-16 h-16 rounded-lg overflow-hidden shadow-md">
                            <img src="<?= $this->asset('images/' . $game['image']) ?>" alt="<?= $this->escape($game['name']) ?>" class="w-full h-full object-cover">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2 -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <div class="flex items-start">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-4 mt-1">2</div>
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <?= str_replace('{game_name}', $this->escape($game['name']), $__('game_guide.view.step_2_title')) ?>
                    </h3>

                    <!-- Game Screenshot -->
                    <div class="text-center">
                        <div class="max-w-lg mx-auto rounded-lg overflow-hidden shadow-md">
                            <img src="<?= $this->asset('images/game-deposit-guide.png') ?>" alt="Game deposit guide" class="w-full h-auto">
                        </div>
                        <p class="text-gray-600 mt-3 text-sm">
                            <?= $__('game_guide.view.instruction_text') ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mt-8">
        <a href="<?= $this->url('/deposit') ?>" class="inline-flex items-center bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            <i class="fas fa-coins mr-2"></i>
            <?= $__('game_guide.view.deposit_now') ?>
        </a>
    </div>
</div>

<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
