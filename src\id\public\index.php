<?php
// Define paths
define('ROOT_PATH', dirname(dirname(__DIR__)));


// Load DomainHelper for dynamic URLs
require_once ROOT_PATH . '/shared/Helpers/DomainHelper.php';

/**
 * Id Domain Entry Point
 * id.mtfgame.com - Auth, Profile & History functionality
 */
// Set timezone
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Configure session for cross-domain sharing
$domainHelper = \Shared\Helpers\DomainHelper::getInstance();
ini_set('session.cookie_domain', $domainHelper->getCookieDomain());
ini_set('session.cookie_path', '/');
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_samesite', 'Lax');

define('ID_PATH', dirname(__DIR__));
define('CORE_PATH', ROOT_PATH . '/core');
define('SHARED_PATH', ROOT_PATH . '/shared');

// Autoloader
spl_autoload_register(function ($class) {
    // Handle different namespaces
    if (strpos($class, 'Id\\App\\') === 0) {
        // Id\App\IdApplication -> IdApplication.php
        $file = str_replace('Id\\App\\', '', $class) . '.php';
        $fullPath = ID_PATH . '/app/' . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }

    if (strpos($class, 'Id\\Controllers\\') === 0) {
        // Id\Controllers\AuthController -> Controllers/AuthController.php
        $file = str_replace('Id\\Controllers\\', 'Controllers/', $class) . '.php';
        $fullPath = ID_PATH . '/app/' . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }

    if (strpos($class, 'Shared\\') === 0) {
        // Shared\Services\ApiService -> Services/ApiService.php
        $file = str_replace('Shared\\', '', $class) . '.php';
        $fullPath = SHARED_PATH . '/' . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }

    if (strpos($class, 'Core\\') === 0) {
        // Core\Application -> Application.php
        $file = str_replace('Core\\', '', $class) . '.php';
        $fullPath = CORE_PATH . '/' . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }

    // Fallback: try direct mapping
    $file = str_replace('\\', '/', $class) . '.php';
    $paths = [
        ROOT_PATH . '/',
        ID_PATH . '/app/',
        CORE_PATH . '/',
        SHARED_PATH . '/'
    ];

    foreach ($paths as $basePath) {
        $fullPath = $basePath . $file;
        if (file_exists($fullPath)) {
            require_once $fullPath;
            return;
        }
    }
});

// Load configuration
$config = require ROOT_PATH . '/config/app.php';

// Update config for id domain
//$config['app']['name'] = 'MtfGame ID';
$config['app']['url'] = $domainHelper->getDomain('id');

try {
    // Create and run id application
    $app = new Id\App\IdApplication($config);
    $app->run();

} catch (Exception $e) {
    // Error handling
    if ($config['app']['debug'] ?? false) {
        echo '<h1>Application Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        echo '<h1>Oops! Something went wrong.</h1>';
        echo '<p>Please try again later.</p>';
    }

    // Log error
    error_log('Id Domain Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
}

