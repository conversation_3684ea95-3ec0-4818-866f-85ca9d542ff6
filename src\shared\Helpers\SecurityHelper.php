<?php

namespace Shared\Helpers;

/**
 * Security Helper Class
 * Provides CSRF token utilities
 */
class SecurityHelper
{
    private static ?SecurityHelper $instance = null;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(): SecurityHelper
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(?string $token): bool
    {
        if (empty($token) || !isset($_SESSION['csrf_token'])) {
            return false;
        }
        return hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Generate captcha code (a-z, 0-9, length 5)
     */
    public function generateCaptcha(): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $captcha = '';

        for ($i = 0; $i < 5; $i++) {
            $captcha .= $characters[random_int(0, strlen($characters) - 1)];
        }

        // Store in session
        $_SESSION['captcha_code'] = strtolower($captcha);
        $_SESSION['captcha_time'] = time();

        return $captcha;
    }

    /**
     * Verify captcha code
     */
    public function verifyCaptcha(?string $userInput): bool
    {
        if (empty($userInput) || !isset($_SESSION['captcha_code'])) {
            return false;
        }

        // Check if captcha has expired (5 minutes)
        if (isset($_SESSION['captcha_time']) && (time() - $_SESSION['captcha_time']) > 300) {
            unset($_SESSION['captcha_code'], $_SESSION['captcha_time']);
            return false;
        }

        $isValid = hash_equals($_SESSION['captcha_code'], strtolower(trim($userInput)));

        // Only clear captcha when verification is successful
        // If wrong, keep captcha so user can try again
        if ($isValid) {
            unset($_SESSION['captcha_code'], $_SESSION['captcha_time']);
        }

        return $isValid;
    }

    /**
     * Generate captcha image
     */
    public function generateCaptchaImage(string $captcha): string
    {
        // Check if GD extension is available
        if (!extension_loaded('gd') || !function_exists('imagecreate')) {
            // Fallback to SVG-based captcha
            return $this->generateSvgCaptcha($captcha);
        }

        try {
            // Create image
            $width = 120;
            $height = 40;
            $image = imagecreate($width, $height);

            // Colors
            $bgColor = imagecolorallocate($image, 240, 240, 240);
            $textColor = imagecolorallocate($image, 50, 50, 50);
            $lineColor = imagecolorallocate($image, 200, 200, 200);

            // Fill background
            imagefill($image, 0, 0, $bgColor);

            // Add noise lines
            for ($i = 0; $i < 5; $i++) {
                imageline($image,
                    random_int(0, $width), random_int(0, $height),
                    random_int(0, $width), random_int(0, $height),
                    $lineColor
                );
            }

            // Add text
            $fontSize = 16;
            $angle = random_int(-10, 10);
            $x = 20;
            $y = 28;

            // Use built-in font if TTF not available
            if (function_exists('imagettftext')) {
                // Try to use a TTF font if available
                $fontPath = __DIR__ . '/../../assets/fonts/arial.ttf';
                if (file_exists($fontPath)) {
                    imagettftext($image, $fontSize, $angle, $x, $y, $textColor, $fontPath, strtoupper($captcha));
                } else {
                    // Fallback to built-in font
                    imagestring($image, 5, $x, 10, strtoupper($captcha), $textColor);
                }
            } else {
                imagestring($image, 5, $x, 10, strtoupper($captcha), $textColor);
            }

            // Output as base64
            ob_start();
            imagepng($image);
            $imageData = ob_get_contents();
            ob_end_clean();
            imagedestroy($image);

            return 'data:image/png;base64,' . base64_encode($imageData);
        } catch (\Exception $e) {
            // Fallback to SVG if GD fails
            return $this->generateSvgCaptcha($captcha);
        }
    }

    /**
     * Generate SVG-based captcha (fallback when GD is not available)
     */
    private function generateSvgCaptcha(string $captcha): string
    {
        $width = 120;
        $height = 40;
        $captchaUpper = strtoupper($captcha);

        // Generate random colors
        $bgColor = sprintf('#%02x%02x%02x', 240, 240, 240);
        $textColor = sprintf('#%02x%02x%02x', 50, 50, 50);
        $lineColor = sprintf('#%02x%02x%02x', 200, 200, 200);

        // Generate noise lines
        $lines = '';
        for ($i = 0; $i < 5; $i++) {
            $x1 = random_int(0, $width);
            $y1 = random_int(0, $height);
            $x2 = random_int(0, $width);
            $y2 = random_int(0, $height);
            $lines .= "<line x1=\"{$x1}\" y1=\"{$y1}\" x2=\"{$x2}\" y2=\"{$y2}\" stroke=\"{$lineColor}\" stroke-width=\"1\"/>";
        }

        // Generate text with slight rotation and spacing
        $textElements = '';
        $charWidth = $width / strlen($captchaUpper);
        for ($i = 0; $i < strlen($captchaUpper); $i++) {
            $char = $captchaUpper[$i];
            $x = ($i * $charWidth) + ($charWidth / 2);
            $y = $height / 2 + 5;
            $rotation = random_int(-10, 10);
            $textElements .= "<text x=\"{$x}\" y=\"{$y}\" font-family=\"Arial, sans-serif\" font-size=\"16\" font-weight=\"bold\" fill=\"{$textColor}\" text-anchor=\"middle\" transform=\"rotate({$rotation} {$x} {$y})\">{$char}</text>";
        }

        $svg = "
        <svg width=\"{$width}\" height=\"{$height}\" xmlns=\"http://www.w3.org/2000/svg\">
            <rect width=\"{$width}\" height=\"{$height}\" fill=\"{$bgColor}\"/>
            {$lines}
            {$textElements}
        </svg>";

        return 'data:image/svg+xml;base64,' . base64_encode($svg);
    }

    /**
     * Refresh captcha (generate new one)
     */
    public function refreshCaptcha(): array
    {
        $captcha = $this->generateCaptcha();
        $image = $this->generateCaptchaImage($captcha);

        return [
            'image' => $image,
            'timestamp' => time()
        ];
    }
}
