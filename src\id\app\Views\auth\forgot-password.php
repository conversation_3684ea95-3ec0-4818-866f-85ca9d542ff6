<?php $this->startSection('content'); ?>

<div class="auth-container px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-6 sm:mb-8">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-primary-500 rounded-xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                <i class="fas fa-key text-white text-lg sm:text-xl"></i>
            </div>
            <h1 class="text-2xl sm:text-3xl font-gaming font-bold text-gray-900 mb-2"><?= $__('auth.forgot_password.view.title') ?></h1>
            <p class="text-gray-600 text-sm sm:text-base"><?= $__('auth.forgot_password.view.subtitle') ?></p>
        </div>

        <div class="auth-form-container rounded-xl p-4 sm:p-6 lg:p-8">
            <!-- Forgot Password Form -->
            <form id="forgotPasswordForm" class="space-y-4 sm:space-y-6">
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-envelope mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.forgot_password.view.email') ?>
                    </label>
                    <div class="relative">
                        <input type="email" name="email" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 pr-10 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.forgot_password.view.enter_email') ?>" required>
                    </div>
                </div>

                <!-- Captcha -->
                <div class="relative">
                    <label class="block text-xs sm:text-sm font-medium text-gray-700 mb-1.5 sm:mb-2">
                        <i class="fas fa-shield-alt mr-1.5 sm:mr-2 text-gray-400 text-xs sm:text-sm"></i>
                        <?= $__('auth.forgot_password.view.captcha') ?>
                    </label>
                    <div class="flex space-x-2 sm:space-x-3">
                        <div class="flex-1">
                            <input type="text" name="captcha" class="w-full px-3 sm:px-4 py-2.5 sm:py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm sm:text-base" placeholder="<?= $__('auth.forgot_password.view.enter_captcha') ?>" required maxlength="5">
                        </div>
                        <div class="flex items-center space-x-1.5 sm:space-x-2">
                            <img id="captchaImage" src="<?= $captcha_image ?>" alt="Captcha" class="h-10 sm:h-12 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" title="<?= $__('auth.forgot_password.view.click_to_refresh') ?>">
                            <button type="button" id="refreshCaptcha" class="h-10 sm:h-12 px-2 sm:px-3 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors" title="<?= $__('auth.forgot_password.view.refresh_captcha') ?>">
                                <i class="fas fa-sync-alt text-gray-600 text-sm"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1"><?= $__('auth.forgot_password.view.captcha_instruction') ?></p>
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-emerald-500 to-blue-600 text-white py-2.5 sm:py-3 px-4 rounded-lg font-gaming font-semibold hover:from-emerald-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-base sm:text-lg">
                    <span class="btn-text"><?= $__('auth.forgot_password.view.send_reset_link') ?></span>
                </button>
            </form>

            <!-- Back to Login -->
            <div class="text-center mt-4 sm:mt-6">
                <p class="text-gray-600 text-xs sm:text-sm"><?= $__('auth.forgot_password.view.remember_password') ?>
                    <a href="<?= $this->url('/') ?>" class="text-blue-600 hover:text-blue-500 font-semibold ml-1"><?= $__('auth.forgot_password.view.login_now') ?></a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php $this->endSection(); ?>

<?php $this->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    // Refresh captcha functionality
    function refreshCaptcha() {
        const refreshBtn = $('#refreshCaptcha');
        const originalIcon = refreshBtn.find('i');

        // Show loading state
        originalIcon.removeClass('fa-sync-alt').addClass('fa-spinner fa-spin');
        refreshBtn.prop('disabled', true);

        $.ajax({
            url: '<?= $this->url('/refresh-captcha') ?>',
            method: 'POST',
            dataType: 'json',
            success: function(response) {
                if (response.status && response.data) {
                    $('#captchaImage').attr('src', response.data.image);
                    $('input[name="captcha"]').val('').removeClass('border-red-500');
                } else {
                    GamingApp.showNotification(window.translations?.forgot_password_js?.captcha_refresh_failed || 'Không thể làm mới mã xác thực', 'error');
                }
            },
            error: function() {
                GamingApp.showNotification(window.translations?.forgot_password_js?.captcha_refresh_failed || 'Không thể làm mới mã xác thực', 'error');
            },
            complete: function() {
                // Restore button state
                originalIcon.removeClass('fa-spinner fa-spin').addClass('fa-sync-alt');
                refreshBtn.prop('disabled', false);
            }
        });
    }

    // Forgot password form
    $('#forgotPasswordForm').submit(function(e) {
        e.preventDefault();

        const formData = {
            email: $('input[name="email"]').val(),
            captcha: $('input[name="captcha"]').val(),
            csrf_token: $('input[name="csrf_token"]').val()
        };

        const submitBtn = $('.gaming-btn-primary');
        const originalText = submitBtn.find('.btn-text').text();

        submitBtn.prop('disabled', true).find('.btn-text').text(window.translations?.forgot_password_js?.sending || 'Đang gửi...');

        $.ajax({
            url: '<?= $this->url('/forgot-password') ?>',
            method: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.status) {
                    GamingApp.showNotification(response.message, 'success');
                    // Clear form
                    $('#forgotPasswordForm')[0].reset();
                    // Refresh captcha on success (captcha was cleared on server)
                    refreshCaptcha();
                } else {
                    // Response có status false nhưng HTTP status là 200
                    GamingApp.showNotification(response.message, 'error');

                    // Only refresh captcha if it's NOT a captcha error
                    // If captcha error, keep the same captcha for retry
                    if (!response.message.includes(window.translations?.forgot_password_js?.captcha_error_check || 'Mã xác thực')) {
                        refreshCaptcha();
                    } else {
                        // Clear only captcha input for retry
                        $('input[name="captcha"]').val('').focus();
                    }
                }

                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            },
            error: function(xhr) {
                let errorMessage = window.translations?.forgot_password_js?.error_occurred || 'Đã xảy ra lỗi. Vui lòng thử lại.';

                try {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        errorMessage = response.message;
                    }
                    if (response && response.errors) {
                        // Hiển thị validation errors
                        Object.keys(response.errors).forEach(field => {
                            const input = $(`input[name="${field}"]`);
                            input.addClass('border-red-500');
                            GamingApp.showNotification(response.errors[field][0], 'error');
                        });
                        submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
                        return;
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }

                GamingApp.showNotification(errorMessage, 'error');

                // Don't auto refresh captcha on general errors
                // Let user manually refresh if needed

                submitBtn.prop('disabled', false).find('.btn-text').text(originalText);
            }
        });
    });

    // Refresh captcha on button click
    $('#refreshCaptcha').click(refreshCaptcha);

    // Refresh captcha on image click
    $('#captchaImage').click(refreshCaptcha);

    // Clear error styling on input focus
    $('input').focus(function() {
        $(this).removeClass('border-red-500');
    });
});
</script>
<?php $this->endSection(); ?>

<!-- Include the shared layout -->
<?php include dirname(dirname(dirname(dirname(__DIR__)))) . '/shared/Views/layouts/app.php'; ?>
