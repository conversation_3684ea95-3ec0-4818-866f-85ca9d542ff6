<?php

namespace Id\Controllers;

use Core\Controller;
use Shared\Services\ApiService;
use Shared\Services\FirebaseService;

/**
 * Index Controller for Id Domain
 * Handles root path - login form or redirect to profile
 */
class IndexController extends Controller
{
    private ApiService $apiService;
    private FirebaseService $firebaseService;

    public function __construct()
    {
        parent::__construct();
        $this->apiService = new ApiService();
        $this->firebaseService = new FirebaseService();

        // Override view to use ID domain views
        $idViewPath = __DIR__ . '/../Views/';
        $this->view = new \Core\View($idViewPath);
    }

    /**
     * Handle root path of ID domain
     */
    public function index(): void
    {
        // If already authenticated, redirect to profile
        if ($this->isAuthenticated()) {
            // Check for return URL first
            $returnUrl = $_GET['return'] ?? null;

            if ($returnUrl) {
                // Validate return URL is from pay domain
                $decodedUrl = urldecode($returnUrl);
                $payDomain = $this->getDomainUrl('pay');
                if (strpos($decodedUrl, $payDomain) !== false) {
                    $this->redirect($decodedUrl);
                    return;
                }
            }

            // Default redirect to profile
            $this->redirect('/profile');
            return;
        }

        // Not authenticated - show login form at root
        $returnUrl = $_GET['return'] ?? null;

        // Generate SEO data
        $seoData = $this->seoHelper->generateSeoData('login');
        $structuredData = $this->seoHelper->generateStructuredData('website');

        $this->render('auth/login', array_merge($this->getThemeData(), [
            'title' => 'Đăng nhập - MtfGame',
            'firebase_config' => $this->firebaseService->getFirebaseConfig(),
            'oauth_providers' => $this->firebaseService->getSupportedProviders(),
            'return_url' => $returnUrl,
            'is_root_login' => true, // Flag to indicate this is root login
            'seoData' => $seoData,
            'structuredData' => $structuredData,
            'csrf_token' => $this->securityHelper->generateCsrfToken()
        ]));
    }
}
