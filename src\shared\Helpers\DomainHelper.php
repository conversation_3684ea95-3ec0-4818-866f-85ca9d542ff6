<?php

namespace Shared\Helpers;

/**
 * Domain Helper
 * Provides centralized domain URL management
 */
class DomainHelper
{
    private static ?DomainHelper $instance = null;
    private ?array $config = null;

    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(): DomainHelper
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load domain configuration
     */
    private function loadConfig(): array
    {
        if ($this->config === null) {
            // Try multiple possible paths for the config file
            $possiblePaths = [
                // From shared/Helpers/ -> ../../config/domains.php
                dirname(dirname(__DIR__)) . '/config/domains.php',
                // From Docker container structure
                '/var/www/html/config/domains.php',
                // Fallback to relative path
                __DIR__ . '/../../config/domains.php'
            ];

            $configPath = null;
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    $configPath = $path;
                    break;
                }
            }

            if ($configPath === null) {
                // Fallback to hardcoded config if file not found
                $this->config = [
                    'id' => 'http://id.mtfgame.com',
                    'pay' => 'http://pay.mtfgame.com',
                    'api' => [
                        'refresh_balance' => 'http://id.mtfgame.com/api/refresh-balance',
                        'auth' => [
                            'login' => 'http://id.mtfgame.com/',
                            'logout' => 'http://id.mtfgame.com/logout',
                            'register' => 'http://id.mtfgame.com/register',
                            'forgot_password' => 'http://id.mtfgame.com/forgot-password',
                        ]
                    ],
                    'pages' => [
                        'profile' => 'http://id.mtfgame.com/profile',
                        'history' => 'http://id.mtfgame.com/history',
                        'deposit' => 'http://pay.mtfgame.com/deposit',
                        'home' => [
                            'id' => 'http://id.mtfgame.com/',
                            'pay' => 'http://pay.mtfgame.com/'
                        ]
                    ]
                ];
            } else {
                $this->config = require $configPath;
            }
        }
        return $this->config;
    }

    /**
     * Get domain URL
     */
    public function getDomain(string $domain): string
    {
        $config = $this->loadConfig();
        return $config[$domain] ?? '';
    }

    /**
     * Get API endpoint URL
     */
    public function getApiUrl(string $endpoint): string
    {
        $config = $this->loadConfig();

        // Support nested endpoints like 'auth.login'
        $parts = explode('.', $endpoint);
        $url = $config['api'];

        foreach ($parts as $part) {
            if (isset($url[$part])) {
                $url = $url[$part];
            } else {
                return '';
            }
        }

        return is_string($url) ? $url : '';
    }

    /**
     * Get page URL
     */
    public function getPageUrl(string $page, ?string $domain = null): string
    {
        $config = $this->loadConfig();

        if ($domain && isset($config['pages'][$page][$domain])) {
            return $config['pages'][$page][$domain];
        }

        // Handle case where page config is an array but no specific domain provided
        $pageConfig = $config['pages'][$page] ?? '';
        if (is_array($pageConfig)) {
            // If it's an array, try to return a default or first available URL
            if (isset($pageConfig['id'])) {
                return $pageConfig['id'];
            } elseif (isset($pageConfig['pay'])) {
                return $pageConfig['pay'];
            } else {
                return array_values($pageConfig)[0] ?? '';
            }
        }

        return $pageConfig;
    }

    /**
     * Get current domain (id or pay)
     */
    public function getCurrentDomain(): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        $config = $this->loadConfig();

        // Extract domain names from full URLs for comparison
        $idDomain = parse_url($config['id'], PHP_URL_HOST);
        $payDomain = parse_url($config['pay'], PHP_URL_HOST);

        if ($idDomain && strpos($host, $idDomain) !== false) {
            return 'id';
        } elseif ($payDomain && strpos($host, $payDomain) !== false) {
            return 'pay';
        }

        // Default to 'id' instead of 'unknown' to prevent errors
        return 'id';
    }

    /**
     * Get home URL for current domain
     */
    public function getHomeUrl(): string
    {
        $currentDomain = $this->getCurrentDomain();
        return $this->getPageUrl('home', $currentDomain);
    }

    /**
     * Get login URL with return parameter
     */
    public function getLoginUrl(?string $returnUrl = null): string
    {
        $loginUrl = $this->getApiUrl('auth.login');

        if ($returnUrl) {
            $loginUrl .= '?return=' . urlencode($returnUrl);
        }

        return $loginUrl;
    }

    /**
     * Get logout URL with return parameter
     */
    public function getLogoutUrl(?string $returnUrl = null): string
    {
        $logoutUrl = $this->getApiUrl('auth.logout');

        if ($returnUrl) {
            $logoutUrl .= '?return=' . urlencode($returnUrl);
        }

        return $logoutUrl;
    }

    public function getCookieDomain(): string
    {
        $config = $this->loadConfig();
        return $config['session']['cookie_domain'] ?? '';
    }

    /**
     * Build URL with return parameter
     */
    public function buildUrlWithReturn(string $baseUrl, ?string $returnUrl = null): string
    {
        if ($returnUrl) {
            $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
            return $baseUrl . $separator . 'return=' . urlencode($returnUrl);
        }

        return $baseUrl;
    }

    /**
     * Get current domain URL based on server name
     */
    public function getCurrentDomainUrl(): string
    {
        $currentDomain = $this->getCurrentDomain();
        return $this->getDomain($currentDomain);
    }
}
